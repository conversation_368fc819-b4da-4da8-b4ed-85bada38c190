{"app": {"title": "Vitamins.ae", "language": "Language"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "name": "Full Name", "welcomeMessage": "Welcome to the Future of Vitamins in UAE", "welcomeDescription": "Discover a world of premium products and exceptional shopping experiences.", "rememberMe": "Remember me"}, "dashboard": {"welcome": "Welcome to your Dashboard", "orders": "Orders", "preorders": "Preorders", "earnings": "Earnings", "customers": "Customers", "products": "Products", "settings": "Settings", "logout": "Logout", "profile": "Profile Settings", "stats": {"totalSales": "Total Sales", "totalOrders": "Total Orders", "averageOrder": "Average Order", "conversionRate": "Conversion Rate", "totalCustomer": "Total Customer", "totalVendor": "Total Vendor", "totalProducts": "Total Products", "promotions": "Promotions", "topProduct": "Top Product", "topVendor": "Top Vendor", "totalCategory": "Total Category", "totalBrand": "Total Brand", "topCategory": "Top Category", "inHouseProducts": "In-House Products", "platformRevenue": "Platform Revenue", "orderCount": "Order Count", "taskPriority": "Task Priority", "pendingTasks": "Pending Tasks"}, "details": {"topCustomer": "Top Customer", "visitors": "Visitors", "buyers": "Buyers", "approvedVendor": "Approved Vendor", "topVendorList": "Top Vendor", "pendingVendor": "Pending Vendor", "inHouse": "In-house", "vendorProducts": "Vendor Products", "productsOnPromotion": "Products on Promotion", "sold": "Sold", "bySalesRating": "By Sales/Rating", "byPeriod": "Monthly Sale", "saleAmount": "Sale Amount", "bySource": "By In-house/Vendor", "byCategory": "By Top Category", "byBrand": "By Top Brand", "byPlatform": "By Entire Platform", "fromVendor": "From Vendor", "placed": "Placed", "confirmed": "Confirmed", "processed": "Processed", "pending": "Pending", "taskList": "Task List", "orderStatus": "Order Status", "advertisingRevenue": "Advertising Revenue", "shippingExpense": "Shipping Expense", "tasks": "Tasks", "remaining": "% remaining", "noTasksInProgress": "No tasks in progress", "completed": "Completed", "inquiries": "inquiries", "products": "products", "items": "items"}, "charts": {"timeSales": "Time-based Sales", "categorySales": "Category Wise Sales", "brandSales": "Brand Wise Sales"}}, "navigation": {"dashboard": "Dashboard", "orders": "Orders", "allorders": "All Orders", "pendingorders": "Pending Orders", "completedorders": "Completed Orders", "cancelledorders": "Cancelled Orders", "products": "Products", "allproducts": "All Products", "categories": "Categories", "category": "Category", "brand": "Brand", "productreviews": "Product Reviews", "catalog": "Catalog", "digitalproducts": "Digital Products", "physicalproducts": "Physical Products", "customers": "Customers", "salesandorder": "Sales and Order", "partnervendors": "Partner Vendors", "reports": "Reports", "salesreports": "Sales Reports", "inventoryreports": "Inventory Reports", "customerreports": "Customer Reports", "vendorreports": "Vendor Reports", "blogmanagement": "Blog Management", "allposts": "All Posts", "comments": "Comments", "marketing": "Marketing", "campaigns": "Campaigns", "promotions": "Promotions", "coupons": "Coupons", "support": "Support", "homepagesetup": "Homepage Setup", "staffsadminmanager": "Staffs/Admin Manager", "users": "Users", "overview": "Overview", "usermanagement": "User Management", "delivery": "Delivery", "loyaltyandrewardpoints": "Loyalty and Reward Points", "demoform": "Demo Form", "demolist": "Demo List", "settings": "Settings", "roles": "Roles", "permissions": "Permissions", "staticpages": "Information Pages", "configurations": "Configurations", "banners": "Banners", "banneritem": "Banner Item", "class": "Class", "blog": "Blog", "attribute": "Attribute", "vendormanagement": "Vendor Management", "vendor": "Vendor EOI", "addproduct": "Add Products", "setting": "Settings", "dropdownmenu": "Dropdown"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "passwordLength": "Password must be at least 8 characters", "passwordMatch": "Passwords must match"}, "demo": {"list": {"title": "Product List", "search": "Search products...", "category": "Category", "status": "Status", "reset": "Reset", "show": "Show", "perPage": "per page", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "emptyMessage": "No products found matching your criteria.", "columns": {"id": "ID", "name": "Product Name", "category": "Category", "price": "Price", "rating": "Rating", "status": "Status"}, "statuses": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "discontinued": "Discontinued"}, "categories": {"electronics": "Electronics", "clothing": "Clothing", "homeKitchen": "Home & Kitchen", "books": "Books", "toys": "Toys"}}, "form": {"title": "Form Components", "formData": "Form Data", "submittedData": "Submitted Form Data:", "noDataMessage": "No form data submitted yet. Fill out and submit the form to see the data here.", "cancel": "Cancel", "submit": "Submit", "submitting": "Submitting...", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "emailHelper": "We'll never share your email with anyone else.", "password": "Password", "passwordHelper": "Your password must be at least 8 characters long.", "bio": "Bio", "bioHelper": "Maximum 200 characters", "bioPlaceholder": "Tell us about yourself...", "birthDate": "Birth Date", "birthDateHelper": "Optional: Select your date of birth", "gender": "Gender", "country": "Country", "countryPlaceholder": "Select your country", "employmentStatus": "Employment Status", "notifications": "Receive Notifications", "notificationsHelper": "We'll send you updates about our services.", "terms": "I agree to the terms and conditions", "richText": "Rich Content", "richTextPlaceholder": "Write your rich formatted content here"}, "genders": {"male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}, "employment": {"fullTime": "Full-time", "partTime": "Part-time", "selfEmployed": "Self-employed", "freelance": "Freelance", "unemployed": "Unemployed", "student": "Student", "retired": "Retired"}, "countries": {"us": "United States", "ca": "Canada", "uk": "United Kingdom", "au": "Australia", "de": "Germany", "fr": "France", "jp": "Japan", "in": "India", "br": "Brazil", "ae": "United Arab Emirates"}}}, "blogCategory": {"emptyMessage": "No blog categories found with your search or filters."}, "blog": {"emptyMessage": "No blog found with your search or filters."}, "category": {"title": "Product Category List", "add": "Add Category", "searchPlaceholder": "Search Category list...", "reset": "Reset", "emptyMessage": "No blog Category found with your search or filters.", "editAction": "Edit Category", "deleteAction": "Delete Category", "columns": {"id": "ID", "name_en": "Name (EN)", "name_ar": "Name (AR)", "code": "Code", "type": "Type", "slug": "Slug", "status": "Status", "actions": "Actions"}}, "attributes": {"title": "Product Attribute List", "add": "Add Attribute", "searchPlaceholder": "Search Attribute list...", "reset": "Reset", "emptyMessage": "No Product Attribute found with your search or filters.", "editAction": "Edit Attribute", "deleteAction": "Delete Attribute", "valuesTitle": "Values Information", "addValue": "Add Value"}, "vendor": {"title": "Vendor EOI List", "searchPlaceholder": "Search EOI list...", "emptyMessage": "No Vendor EOI found with your search or filters.", "vendorId": "Vendor ID", "vendorInfo": "Vendor Information", "vendorDirectorInfo": "Vendor Director Information", "vendorSpocInfo": "Vendor Contact Person Information", "vendorSkuInfo": "Stock Keeping Unit Information", "vendorTLInfo": "Trade License Information", "vendorTRInfo": "Tax Registration Information", "vendorSocialInfo": "Social Media Information", "vendorAdditionalInfo": "Vendor Additional Information", "vendorConfirmApproval": "Confirm <PERSON><PERSON><PERSON>"}, "dropdownMenu": {"title": "DropDown List", "add": "Add DropDown", "searchPlaceholder": "Search DropDown list...", "emptyMessage": "No DropDown found with your search or filters.", "editAction": "Edit DropDown", "deleteAction": "Delete DropDown"}, "commonPagination": {"showing": "Showing", "to": "to", "of": "of", "results": "results", "show": "Show", "perPage": "Per Page"}, "commonDelete": {"deleteTitle": "Confirm Delete", "deleteMessage": "Are you sure you want to delete the", "deleteWarning": " This action cannot be undone."}, "commonOptions": {"categoryType": {"main": "Main Category", "sub": "Sub Category"}, "status": {"active": "Active", "inactive": "Inactive"}, "yesNo": {"Yes": "Yes", "No": "No"}}, "commonButton": {"add": "Add", "cancel": "Cancel", "delete": "Delete", "remove": "Remove", "reset": "Reset", "approval": "Approved", "category": {"create": "Create Category", "updated": "Update Category"}, "attribute": {"create": "Create Attribute", "updated": "Update Attribute"}, "dropDown": {"create": "Create DropDown", "updated": "Update DropDown"}}, "commonTableLabel": {"id": "ID", "name_en": "Name (EN)", "name_ar": "Name (AR)", "vendor_name_en": "Vendor Name (EN)", "vendor_name_ar": "Vendor Name (AR)", "approval_status": "Approval Status", "status": "Status", "actions": "Actions", "previewAction": "Preview page", "businessType": "Business Type", "website": "Website", "passportName": "Name (As on Passport)", "publicName": "Public Name", "designation": "Designation", "mobile": "Mobile Number", "email": "Email", "preferredLanguage": "Preferred Language", "passportNumber": "Passport Number", "eID": "National ID", "eIDIssueDate": "National ID Issue Date", "eIDExpiryDate": "National ID Expiry Date", "onAmazon": "Amazon", "onNoon": "<PERSON>on", "onOtherMarketplaces": "Other Marketplaces", "onOwnWebsite": "Own Website", "letterAuthorization": "SPOC Letter of Authorization", "tlType": "Entity Type", "issuingAuthority": "Issuing Authority", "issuingDate": "Issuing Date", "expiryDate": "Expiry Date", "taxRegNumber": "Tax Registration Number", "facebook": "facebook", "instagram": "Instagram", "brandSell": "Brands to Sell", "categoriesToSell": "Categories to Sell", "distributorStores": "Distributor Stores", "importerBrands": "Imported Brands", "manufacturerBrands": "Manufactured Brands", "retailerTotalOutlets": "Total Retail Outlets", "additionalInfo": "Additional Info", "key": "Key", "value_en": "Value (EN)", "value_ar": "Value (AR)", "sort_order": "Sort Order"}, "commonField": {"name_en": "Name (In English)", "name_ar": "Name (In Arabic)", "code": "Code", "fee_text": "Fee", "slug": "Slug", "meta_options": "Meta Options", "meta_title_en": "Meta Title (In English)", "meta_description": "Meta Description", "category_type": "Category Type", "selectMainCategory": "Select Main Category", "status": "Status", "ordering_serial": "Ordering Serial", "banner": "Upload Banner", "cover_image": "Upload Cover", "icon": "Upload Icon", "value_en": "Value (In English)", "value_ar": "Value (In Arabic)", "approval": "Approved"}, "commonPlaceholder": {"name_enPlaceholder": "Enter the name in English", "name_arPlaceholder": "Enter the name in Arabic", "codePlaceholder": "Enter the code", "fee_textPlaceholder": "Enter the fee", "slugPlaceholder": "Enter the slug", "meta_titlePlaceholder": "Enter the meta title in English", "meta_descriptionPlaceholder": "Enter the meta description", "ordering_serialPlaceholder": "Enter the ordering serial", "value_enPlaceholder": "Enter the value in English", "value_arPlaceholder": "Enter the value in Arabic"}, "commonValidation": {"name_en": "Enter the Name is required", "category_type": "Category Type is required", "slug": "Slug is required", "status": "Status is required", "value_en": "Value (EN) is required", "atLeastOneValue": "At least one value is required", "valuesRequired": "Values are required"}, "commonToast": {"categoryToast": {"categoryDelete": "Category Deleted successfully", "categoryCreate": "Category Created successfully", "categoryUpdate": "Category Updated successfully"}, "attributeToast": {"attributeDelete": "Attribute Deleted successfully", "attributeCreate": "Attribute Created successfully", "attributeUpdate": "Attribute Updated successfully"}, "dropDownToast": {"dropDownDelete": "DropDown Deleted successfully", "dropDownCreate": "DropDown Created successfully", "dropDownUpdate": "DropDown Updated successfully"}}, "products": {"emptyMessage": "No products found matching your criteria.", "searchPlaceholder": "Search products by name, SKU, or description...", "addProduct": "Add Product", "editProduct": "Edit Product", "createProduct": "Create New Product", "productManagement": "Product Management", "allProducts": "All Products", "productDetails": "Product Details", "basicInformation": "Basic Information", "descriptionContent": "Description & Content", "mediaManagement": "Media Management", "pricingInventory": "Pricing & Inventory", "productVariants": "Product Variants", "fulfillmentLogistics": "Fulfillment & Logistics", "complianceCertifications": "Compliance & Certifications", "seoOptimization": "SEO Optimization", "faqsManagement": "FAQs Management", "reviewSubmit": "Review & Submit", "saveDraft": "Save Draft", "submitForApproval": "Submit for Approval", "overallProgress": "Overall Progress", "stepsCompleted": "steps completed", "complete": "Complete", "autoSaving": "Auto-saving...", "layoutToggle": "Layout Toggle", "horizontalLayout": "Horizontal Layout", "verticalLayout": "Vertical Layout", "step": "Step", "of": "of", "completed": "completed", "previous": "Previous", "next": "Next", "cancel": "Cancel", "filters": {"allStatus": "All Status", "active": "Active", "draft": "Draft", "inactive": "Inactive", "allCategories": "All Categories", "allBrands": "All Brands", "allPrices": "All Prices", "allStock": "All Stock", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "priceRange": "Price Range", "stockStatus": "Stock Status", "clear": "Clear", "activeFilters": "Active filters"}, "table": {"image": "Image", "productName": "Product Name", "sku": "SKU", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "offer": "Offer", "primary": "Primary"}, "messages": {"deleteConfirm": "Are you sure you want to delete this product?", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "saveSuccess": "Product saved successfully", "saveError": "Failed to save product", "submitSuccess": "Product submitted for approval successfully", "submitError": "Failed to submit product", "incompleteStep": "Please fill in all required fields before proceeding", "incompleteProduct": "Please complete at least 80% of the form before submitting", "autoSaveError": "Auto-save failed"}, "completeStepsDescription": "Complete all steps to create and publish your product"}}