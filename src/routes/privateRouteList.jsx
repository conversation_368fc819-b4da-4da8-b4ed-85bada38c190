import { lazy } from "react";
import React from "react";
import * as permissions from "../constants/permissions";

// Lazy loaded pages
const Dashboard = lazy(() => import("../pages/dashboard/Dashboard"));
const FormDemo = lazy(() => import("../pages/demo/FormDemo"));
const ListDemo = lazy(() => import("../pages/demo/ListDemo"));

// user management component start
// const UserManagement = lazy(() => import("../pages/staff/UserManagement"));
const UserIndex = lazy(() => import("../pages/userManagement/user/UserIndex"));
const RoleIndex = lazy(() => import("../pages/userManagement/role/RoleIndex"));
const PermissionIndex = lazy(() =>
  import("../pages/userManagement/permission/PermissionIndex")
);
// user management component end

//page components start
const PageIndex = lazy(() => import("../pages/page/PageIndex"));
//page components end

// Customers components start
const CustomersPage = lazy(() => import("../pages/customer/customerIndex"));
// Customers components end

// Configuration components start
const BannerPage = lazy(() =>
  import("../pages/configuration/Banner/BannerIndex")
);
const BannerItemPage = lazy(() =>
  import("../pages/configuration/BannerItem/BannerItemIndex")
);
// Configuration components end

// Product components start

// Category
const ProductCategoriesPage = lazy(() =>
  import("../pages/product/category/CategoryIndex")
);
const ProductCategoriesAddEditPage = lazy(() =>
  import("../pages/product/category/CategoryAddEditPage")
);
// product class
const ProductClassPage = lazy(() =>
  import("../pages/product/productClass/ProductClassIndex")
);

// Attribute
const ProductAttributePage = lazy(() =>
  import("../pages/product/productAttribute/attributeIndex")
);
const ProductAttributeAddEditPage = lazy(() =>
  import("../pages/product/productAttribute/attributeAddEditPage")
);

// Product components end

// Vendor Management start
//vendor eoi
const VendorEOIPage = lazy(() =>
  import("../pages/vendorManagement/vendorEoi/eoiIndex")
);
const VendorEOIPreviewPage = lazy(() =>
  import("../pages/vendorManagement/vendorEoi/eoiPreview")
);
// Vendor Management end

// settings Menu start

const DropDownOptionPage = lazy(() =>
  import("../pages/setting/dropdownMenu/DropDownIndex")
);

const DropDownAddEditPage = lazy(() =>
  import("../pages/setting/dropdownMenu/dropDownAddEditPage")
);

// settings Menu end.

// Brand Start
const BrandPage = lazy(() =>
  import("../pages/brandManagement/brand/brandIndex")
);
// Brand End

// Simple page components
// Orders
const AllOrdersPage = () => <div>All Orders Page</div>;
const PendingOrdersPage = () => <div>Pending Orders Page</div>;
const CompletedOrdersPage = () => <div>Completed Orders Page</div>;
const CancelledOrdersPage = () => <div>Cancelled Orders Page</div>;

// Products
const AllProductsPage = () => <div>All Products Page</div>;
const ProductBrandsPage = () => <div>Product Brands Page</div>;
const ProductReviewsPage = () => <div>Product Reviews Page</div>;
const DigitalProductsPage = () => <div>Digital Products Page</div>;
const PhysicalProductsPage = () => <div>Physical Products Page</div>;

// Vendors
const VendorsPage = () => <div>Partner Vendors Page</div>;

// Reports
const SalesReportsPage = () => <div>Sales Reports Page</div>;
const InventoryReportsPage = () => <div>Inventory Reports Page</div>;
const CustomerReportsPage = () => <div>Customer Reports Page</div>;
const VendorReportsPage = () => <div>Vendor Reports Page</div>;

// Products
const ProductListPage = lazy(() =>
  import("../pages/products/allProducts/ProductIndex")
);
const ProductWizardPage = lazy(() =>
  import("../pages/products/productWizard/ProductWizardPage")
);

// Product Circle start
const ProductCircleIndexPage = lazy(() =>
  import("../pages/product/productCircle/ProductCircleIndex")
);
const ProductCircleAddEditPage = lazy(() =>
  import("../pages/product/productCircle/ProductCircleAddEditPage")
);

// Product Circle end

// Blog
// const BlogPostsPage = () => <div>Blog Posts Page</div>;
const BlogPostsPage = lazy(() => import("../pages/blog/allBlog/blogIndex"));
const BlogCategoriesPage = lazy(() =>
  import("../pages/blog/blogCategory/blogCategoryIndex")
);
const BlogAddEditPage = lazy(() =>
  import("../pages/blog/blogAddEdit/BlogAddEditPage")
);
// const BlogCategoriesPage = () => <div>Blog Categories Page</div>;
const BlogCommentsPage = () => <div>Blog Comments Page</div>;

// Marketing
const MarketingCampaignsPage = () => <div>Marketing Campaigns Page</div>;
const MarketingPromotionsPage = () => <div>Marketing Promotions Page</div>;
const MarketingCouponsPage = () => <div>Marketing Coupons Page</div>;

// Other Pages
const SupportPage = () => <div>Support Page</div>;
const HomepageSetupPage = () => <div>Homepage Setup Page</div>;
const StaffAdminPage = () => <div>Staff/Admin Manager Page</div>;
const DeliveryPage = () => <div>Delivery Page</div>;
const LoyaltyPage = () => <div>Loyalty and Reward Points Page</div>;
const SettingsPage = () => <div>Settings Page</div>;

/**
 * Private routes configuration
 * These routes require authentication to access
 */
const privateRoutes = [
  // Dashboard
  {
    path: "/dashboard",
    component: Dashboard,
    permissions: [
      permissions.BROWSE_ORDERS,
      permissions.BROWSE_PRODUCTS,
      permissions.BROWSE_USERS,
      permissions.BROWSE_VENDORS,
    ],
  },

  // Orders
  {
    path: "/orders",
    component: AllOrdersPage,
    permissions: [permissions.BROWSE_ORDERS],
  },
  {
    path: "/orders/pending",
    component: PendingOrdersPage,
    permissions: [permissions.BROWSE_ORDERS],
  },
  {
    path: "/orders/completed",
    component: CompletedOrdersPage,
    permissions: [permissions.BROWSE_ORDERS],
  },
  {
    path: "/orders/cancelled",
    component: CancelledOrdersPage,
    permissions: [permissions.BROWSE_ORDERS],
  },

  // Products
  {
    path: "/products/all",
    component: ProductListPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/add",
    component: ProductWizardPage,
    permissions: [permissions.CREATE_PRODUCT],
  },
  {
    path: "/products/edit/:id",
    component: ProductWizardPage,
    permissions: [permissions.EDIT_PRODUCT],
  },
  //product circle
  {
    path: "/products/list",
    component: ProductCircleIndexPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/list/add",
    component: ProductCircleAddEditPage,
    permissions: [permissions.CREATE_PRODUCT],
  },
  {
    path: "/products/list/edit/:id",
    component: ProductCircleAddEditPage,
    permissions: [permissions.CREATE_PRODUCT],
  },

  {
    path: "/products/categories",
    component: ProductCategoriesPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/categories/add",
    component: ProductCategoriesAddEditPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/categories/edit/:id",
    component: ProductCategoriesAddEditPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },
  {
    path: "/products/class",
    component: ProductClassPage,
    permissions: [permissions.BROWSE_CATEGORIES],
  },

  {
    path: "/products/reviews",
    component: ProductReviewsPage,
    permissions: [permissions.BROWSE_REVIEWS],
  },
  {
    path: "/products/catalog/digital",
    component: DigitalProductsPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/catalog/physical",
    component: PhysicalProductsPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  // Attribute
  {
    path: "/products/attribute",
    component: ProductAttributePage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/attribute/add",
    component: ProductAttributeAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/products/attribute/edit/:id",
    component: ProductAttributeAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // DropDown Setting
  {
    path: "/setting/menu/dropdown",
    component: DropDownOptionPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/setting/menu/dropdown/add",
    component: DropDownAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/setting/menu/dropdown/edit/:id",
    component: DropDownAddEditPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // Brand Management
  {
    path: "/brandManagement/brand",
    component: BrandPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // Vendor Management
  {
    path: "/vendor/eoi",
    component: VendorEOIPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },
  {
    path: "/vendor/eoi/preview/:id",
    component: VendorEOIPreviewPage,
    permissions: [permissions.BROWSE_PRODUCTS],
  },

  // Customers and Vendors
  {
    path: "/customers",
    component: CustomersPage,
    permissions: [permissions.BROWSE_USERS],
  },
  {
    path: "/vendors",
    component: VendorsPage,
    permissions: [permissions.BROWSE_VENDORS],
  },

  // Configuration
  {
    path: "/configuration/banners",
    component: BannerPage,
    permissions: [permissions.BROWSE_USERS],
  },
  {
    path: "/configuration/banner/items",
    component: BannerItemPage,
    permissions: [permissions.BROWSE_USERS],
  },

  // Reports
  {
    path: "/reports/sales",
    component: SalesReportsPage,
    permissions: [permissions.VIEW_SALES_REPORTS],
  },
  {
    path: "/reports/inventory",
    component: InventoryReportsPage,
    permissions: [permissions.VIEW_SALES_REPORTS],
  },
  {
    path: "/reports/customers",
    component: CustomerReportsPage,
    permissions: [permissions.ANALYZE_BUYER_BEHAVIOR],
  },
  {
    path: "/reports/vendors",
    component: VendorReportsPage,
    permissions: [permissions.ANALYZE_PRODUCT_PERFORMANCE],
  },

  // Blog
  {
    path: "/blog/posts",
    component: BlogPostsPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/posts/add",
    component: BlogAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/posts/edit/:id",
    component: BlogAddEditPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/categories",
    component: BlogCategoriesPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },
  {
    path: "/blog/comments",
    component: BlogCommentsPage,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
  },

  // Pages
  {
    path: "/pages",
    component: PageIndex,
    permissions: [permissions.PAGE_VIEW],
  },

  // Marketing
  {
    path: "/marketing/campaigns",
    component: MarketingCampaignsPage,
    permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  },
  {
    path: "/marketing/promotions",
    component: MarketingPromotionsPage,
    permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  },
  {
    path: "/marketing/coupons",
    component: MarketingCouponsPage,
    permissions: [permissions.BROWSE_COUPONS],
  },

  // Other Pages
  {
    path: "/support",
    component: SupportPage,
    permissions: [permissions.BROWSE_SUPPORT_TICKETS],
  },
  {
    path: "/homepage-setup",
    component: HomepageSetupPage,
    permissions: [permissions.MANAGE_HOMEPAGE_BANNERS],
  },
  // {
  //   path: "/staff",
  //   component: StaffAdminPage,
  //   permissions: [permissions.STAFF_VIEW],
  // },

  // User Management routes start
  {
    path: "/staff/users",
    component: UserIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  {
    path: "/staff/roles",
    component: RoleIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  {
    path: "/staff/permissions",
    component: PermissionIndex,
    permissions: [permissions.STAFF_VIEW],
  },
  // User Management routes end

  {
    path: "/delivery",
    component: DeliveryPage,
    permissions: [permissions.BROWSE_SHIPMENTS],
  },
  {
    path: "/loyalty",
    component: LoyaltyPage,
    permissions: [permissions.MANAGE_AFFILIATE_AND_REFERRAL_PROGRAMS],
  },
  {
    path: "/settings",
    component: SettingsPage,
    permissions: [permissions.MANAGE_SETTINGS],
  },

  // Demo Form
  {
    path: "/demo/form",
    component: FormDemo,
    permissions: [permissions.MANAGE_SETTINGS],
  },

  // Demo List
  {
    path: "/demo/list",
    component: ListDemo,
    permissions: [permissions.MANAGE_SETTINGS],
  },
];

export default privateRoutes;
