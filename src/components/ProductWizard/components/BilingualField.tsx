
import React from 'react';
import { Input } from '@/components/ui/product/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface BilingualFieldProps {
  label: string;
  type?: 'input' | 'textarea';
  required?: boolean;
  layoutMode: 'horizontal' | 'vertical';
  valueEn: string;
  valueAr: string;
  onChangeEn: (value: string) => void;
  onChangeAr: (value: string) => void;
  placeholder?: {
    en: string;
    ar: string;
  };
}

export default function BilingualField({
  label,
  type = 'input',
  required = false,
  layoutMode,
  valueEn,
  valueAr,
  onChangeEn,
  onChangeAr,
  placeholder = { en: '', ar: '' }
}: BilingualFieldProps) {
  const InputComponent = type === 'textarea' ? Textarea : Input;
  
  const renderField = (lang: 'en' | 'ar', value: string, onChange: (value: string) => void) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-gray-700">
        {label} ({lang === 'en' ? 'English' : 'العربية'})
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <InputComponent
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder[lang]}
        className="w-full"
        dir={lang === 'ar' ? 'rtl' : 'ltr'}
        rows={type === 'textarea' ? 4 : undefined}
      />
    </div>
  );

  if (layoutMode === 'horizontal') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderField('en', valueEn, onChangeEn)}
        {renderField('ar', valueAr, onChangeAr)}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {renderField('en', valueEn, onChangeEn)}
      {renderField('ar', valueAr, onChangeAr)}
    </div>
  );
}
