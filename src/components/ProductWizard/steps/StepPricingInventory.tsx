
import React from 'react';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/product/tabs';
import ProductVariantsTab from '../components/ProductVariantsTab';

interface StepPricingInventoryProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
  validationErrors?: Record<string, string>;
  showValidation?: boolean;
}

// Mock data for demonstration - in real app this would come from API
const mockAttributeList = [
  { id: 1, name: 'Color', slug: 'color' },
  { id: 2, name: 'Si<PERSON>', slug: 'size' },
  { id: 3, name: 'Material', slug: 'material' }
];

const mockAttributeValueMap = {
  1: [
    { id: 1, product_attribute_id: 1, value: 'Red' },
    { id: 2, product_attribute_id: 1, value: 'Blue' },
    { id: 3, product_attribute_id: 1, value: 'Green' },
    { id: 4, product_attribute_id: 1, value: 'Black' },
    { id: 5, product_attribute_id: 1, value: 'White' }
  ],
  2: [
    { id: 6, product_attribute_id: 2, value: 'S' },
    { id: 7, product_attribute_id: 2, value: 'M' },
    { id: 8, product_attribute_id: 2, value: 'L' },
    { id: 9, product_attribute_id: 2, value: 'XL' },
    { id: 10, product_attribute_id: 2, value: 'XXL' }
  ],
  3: [
    { id: 11, product_attribute_id: 3, value: 'Cotton' },
    { id: 12, product_attribute_id: 3, value: 'Polyester' },
    { id: 13, product_attribute_id: 3, value: 'Nylon' },
    { id: 14, product_attribute_id: 3, value: 'Wool' }
  ]
};

export default function StepPricingInventory({
  data,
  onChange,
  layoutMode,
  validationErrors,
  showValidation = false
}: StepPricingInventoryProps) {
  const handleChange = (field: string, value: any) => {
    console.log(`Updating ${field}:`, value); // Debug log
    console.log('Current data before update:', data); // Debug log
    const newData = { ...data, [field]: value };
    console.log('New data after update:', newData); // Debug log
    onChange(newData);
  };

  // Validation for mandatory fields
  const isRegularPriceValid = data.regular_price && data.regular_price > 0;

  const allMandatoryFieldsValid = isRegularPriceValid;
  const shouldShowValidationHint = showValidation && !allMandatoryFieldsValid;

  // Get missing mandatory fields for error message
  const getMissingFields = () => {
    const missing = [];
    if (!isRegularPriceValid) missing.push('Regular Price');
    return missing;
  };

  const handleVariantToggle = (checked: boolean) => {
    console.log('=== VARIANT TOGGLE DEBUG ===');
    console.log('Toggle clicked with value:', checked);
    console.log('Current data.is_variant:', data.is_variant);
    console.log('Current data type:', typeof data.is_variant);
    console.log('Current data object:', data);
    console.log('onChange function:', onChange);

    if (!checked && data.variants && data.variants.length > 0) {
      // Ask for confirmation before clearing variants
      const confirmClear = window.confirm(
        'Disabling variants will remove all variant data. Are you sure you want to continue?'
      );
      if (!confirmClear) {
        console.log('User cancelled variant toggle');
        return; // Don't change the toggle if user cancels
      }
    }

    // Create new data object with updated variant flag
    const newData = { ...data, is_variant: checked };

    if (!checked) {
      // Clear variant-related data when disabling variants
      newData.variants = [];
      newData.attributes = [];
    }

    console.log('About to call onChange with:', newData);
    console.log('New is_variant value:', newData.is_variant);

    // Call onChange and verify it was called
    try {
      onChange(newData);
      console.log('onChange called successfully');
    } catch (error) {
      console.error('Error calling onChange:', error);
    }

    console.log('=== END VARIANT TOGGLE DEBUG ===');
  };

  const calculateDiscount = (regular: number, offer: number) => {
    if (regular && offer) {
      const discount = ((regular - offer) / regular) * 100;
      return discount.toFixed(1);
    }
    return '0';
  };

  return (
    <div className="space-y-6">
      {/* Variant Toggle */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Switch
              id="is_variant"
              checked={Boolean(data.is_variant)}
              onCheckedChange={handleVariantToggle}
            />
            <div className="flex-1">
              <Label htmlFor="is_variant" className="font-medium text-blue-900">
                This product has variants (e.g., sizes, colors, materials)
              </Label>
              <p className="text-sm text-blue-700 mt-1">
                Enable this if your product comes in different variations like sizes, colors, or styles
              </p>
            </div>
            <div className="text-sm font-medium">
              Status: <span className={`px-2 py-1 rounded ${data.is_variant ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                {data.is_variant ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {data.is_variant ? (
        <ProductVariantsTab
          attributeList={mockAttributeList}
          attributeValueMap={mockAttributeValueMap}
          variants={data.variants || []}
          onChange={(variants) => handleChange('variants', variants)}
          productId={data.system_sku || 'PROD'}
          defaultRegularPrice={data.regular_price || 0}
          defaultOfferPrice={data.offer_price || 0}
        />
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Pricing Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="regular_price">Regular Price * (SAR)</Label>
                  <Input
                    id="regular_price"
                    type="number"
                    step="0.01"
                    value={data.regular_price}
                    onChange={(e) => handleChange('regular_price', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="offer_price">Offer Price (SAR)</Label>
                  <Input
                    id="offer_price"
                    type="number"
                    step="0.01"
                    value={data.offer_price}
                    onChange={(e) => handleChange('offer_price', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                  />
                  {data.offer_price > 0 && (
                    <p className="text-sm text-green-600">
                      Discount: {calculateDiscount(data.regular_price, data.offer_price)}% off
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vat_tax">VAT Tax (%)</Label>
                  <Input
                    id="vat_tax"
                    type="number"
                    step="0.01"
                    value={data.vat_tax}
                    onChange={(e) => handleChange('vat_tax', parseFloat(e.target.value) || 0)}
                    placeholder="15"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="approx_commission">Approx. Commission (%)</Label>
                  <Input
                    id="approx_commission"
                    type="number"
                    step="0.01"
                    value={data.approx_commission}
                    onChange={(e) => handleChange('approx_commission', parseFloat(e.target.value) || 0)}
                    placeholder="10"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="discount_start_date">Discount Start Date</Label>
                  <Input
                    id="discount_start_date"
                    type="date"
                    value={data.discount_start_date}
                    onChange={(e) => handleChange('discount_start_date', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="discount_end_date">Discount End Date</Label>
                  <Input
                    id="discount_end_date"
                    type="date"
                    value={data.discount_end_date}
                    onChange={(e) => handleChange('discount_end_date', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Inventory Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stock">Current Stock</Label>
                  <Input
                    id="stock"
                    type="number"
                    value={data.stock}
                    onChange={(e) => handleChange('stock', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reserved">Reserved for Orders</Label>
                  <Input
                    id="reserved"
                    type="number"
                    value={data.reserved}
                    onChange={(e) => handleChange('reserved', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="threshold">Low-stock Warning Level</Label>
                  <Input
                    id="threshold"
                    type="number"
                    value={data.threshold}
                    onChange={(e) => handleChange('threshold', parseInt(e.target.value) || 0)}
                    placeholder="5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stock_status">Stock Status</Label>
                  <Select value={data.stock_status || 'in_stock'} onValueChange={(value) => handleChange('stock_status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select stock status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_stock">In Stock</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                      <SelectItem value="low_stock">Low Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="warehouse_id">Warehouse</Label>
                  <Select value={data.warehouse_id} onValueChange={(value) => handleChange('warehouse_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select warehouse" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="warehouse_1">Main Warehouse</SelectItem>
                      <SelectItem value="warehouse_2">Secondary Warehouse</SelectItem>
                      <SelectItem value="warehouse_3">Distribution Center</SelectItem>
                      <SelectItem value="warehouse_4">Regional Hub</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {shouldShowValidationHint && (
                <div
                  className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200 shadow-sm"
                  role="alert"
                  aria-live="polite"
                  aria-describedby="pricing-validation-message"
                >
                  <div className="flex items-start gap-3">
                    <div className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0">⚠</div>
                    <div>
                      <h5 className="text-sm font-semibold text-red-800 mb-1">
                        Validation Error
                      </h5>
                      <p
                        id="pricing-validation-message"
                        className="text-sm text-red-700"
                      >
                        Please fill in the following required fields: {getMissingFields().join(', ')}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
