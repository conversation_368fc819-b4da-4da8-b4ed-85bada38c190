
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/product/button';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/product/card';
import { Upload, X, Star, GripVertical, ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface StepProductMediaProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
}

interface MediaItem {
  id: string;
  url: string;
  file?: File;
  alt_text: string;
  is_primary: boolean;
  order: number;
}

export default function StepProductMedia({ data, onChange, layoutMode }: StepProductMediaProps) {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleChange = (field: string, value: any) => {
    onChange({ ...data, [field]: value });
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const maxFiles = 6 - data.media.length;
    const filesToAdd = Array.from(files).slice(0, maxFiles);

    if (filesToAdd.length < files.length) {
      toast({
        title: "File limit reached",
        description: `Only ${maxFiles} more images can be added (max 6 total)`,
        variant: "destructive",
      });
    }

    const newMediaItems: MediaItem[] = filesToAdd.map((file, index) => ({
      id: `media_${Date.now()}_${index}`,
      url: URL.createObjectURL(file),
      file,
      alt_text: '',
      is_primary: data.media.length === 0 && index === 0,
      order: data.media.length + index + 1,
    }));

    handleChange('media', [...data.media, ...newMediaItems]);
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const removeMediaItem = (id: string) => {
    const itemToRemove = data.media.find((item: MediaItem) => item.id === id);
    if (itemToRemove?.url.startsWith('blob:')) {
      URL.revokeObjectURL(itemToRemove.url);
    }
    
    const updatedMedia = data.media.filter((item: MediaItem) => item.id !== id);
    
    // If we removed the primary image, make the first one primary
    if (updatedMedia.length > 0 && !updatedMedia.some((item: MediaItem) => item.is_primary)) {
      updatedMedia[0].is_primary = true;
    }
    
    // Reorder items
    const reorderedMedia = updatedMedia.map((item: MediaItem, index: number) => ({
      ...item,
      order: index + 1
    }));
    
    handleChange('media', reorderedMedia);
  };

  const updateMediaItem = (id: string, field: string, value: any) => {
    const updatedMedia = data.media.map((item: MediaItem) =>
      item.id === id ? { ...item, [field]: value } : item
    );
    handleChange('media', updatedMedia);
  };

  const setPrimaryImage = (id: string) => {
    const updatedMedia = data.media.map((item: MediaItem) => ({
      ...item,
      is_primary: item.id === id
    }));
    handleChange('media', updatedMedia);
  };

  const handleItemDragStart = (e: React.DragEvent, id: string) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleItemDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleItemDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = data.media.findIndex((item: MediaItem) => item.id === draggedItem);
    const targetIndex = data.media.findIndex((item: MediaItem) => item.id === targetId);

    const updatedMedia = [...data.media];
    const [draggedMediaItem] = updatedMedia.splice(draggedIndex, 1);
    updatedMedia.splice(targetIndex, 0, draggedMediaItem);

    // Update order numbers
    const reorderedMedia = updatedMedia.map((item: MediaItem, index: number) => ({
      ...item,
      order: index + 1
    }));

    handleChange('media', reorderedMedia);
    setDraggedItem(null);
  };

  return (
    <div className="space-y-6">

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
        />
        
        <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <div className="space-y-2 flex flex-col items-center">
          <Button
            onClick={handleFileUpload}
            disabled={data.media.length >= 6}
            className="flex items-center gap-2"
            variant="outline"
          >
            <Upload className="w-4 h-4" />
            {data.media.length >= 6 ? 'Maximum images reached' : 'Upload Images'}
          </Button>
          <p className="text-sm text-gray-500">
            Drag and drop images here, or click to browse ({data.media.length}/6)
          </p>
          <p className="text-xs text-gray-400">
            Supports: JPG, PNG, GIF, WebP (max 5MB each)
          </p>
        </div>
      </div>

      {/* Media Grid */}
      {data.media.length > 0 && (
        <>
          <div className="flex justify-between items-center">
            <h4 className="font-medium text-gray-900">Uploaded Images</h4>
            <p className="text-sm text-gray-500">
              Drag to reorder • Click star to set as primary
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.media.map((item: MediaItem, index: number) => (
              <Card 
                key={item.id} 
                className={`relative cursor-move transition-transform hover:scale-105 ${
                  draggedItem === item.id ? 'opacity-50' : ''
                }`}
                draggable
                onDragStart={(e) => handleItemDragStart(e, item.id)}
                onDragOver={handleItemDragOver}
                onDrop={(e) => handleItemDrop(e, item.id)}
              >
                <CardContent className="p-4">
                  <div className="relative mb-4 group">
                    <img
                      src={item.url}
                      alt={item.alt_text || `Product image ${index + 1}`}
                      className="w-full h-40 object-cover rounded-lg"
                    />
                    
                    {/* Drag handle */}
                    <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-black bg-opacity-50 rounded p-1">
                        <GripVertical className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant={item.is_primary ? "default" : "outline"}
                        onClick={() => setPrimaryImage(item.id)}
                        className="h-8 w-8 p-0 bg-white hover:bg-gray-100"
                      >
                        <Star className={`w-4 h-4 ${item.is_primary ? 'fill-yellow-400 text-yellow-400' : 'text-gray-600'}`} />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeMediaItem(item.id)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Primary badge */}
                    {item.is_primary && (
                      <div className="absolute bottom-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                        Primary
                      </div>
                    )}

                    {/* Order number */}
                    <div className="absolute bottom-2 right-2 bg-gray-900 bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                      #{item.order}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`alt_text_${item.id}`} className="text-sm">
                      Alt Text (for accessibility)
                    </Label>
                    <Input
                      id={`alt_text_${item.id}`}
                      value={item.alt_text}
                      onChange={(e) => updateMediaItem(item.id, 'alt_text', e.target.value)}
                      placeholder="Describe this image..."
                      className="text-sm"
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
