import { useField } from 'formik';
import { motion } from 'framer-motion';
import { useContext } from 'react';
import { LanguageContext } from '../../../contexts/LanguageContext';

const FormSwitch = ({ label, helperText, className = '', ...props }) => {
  const [field, meta, helpers] = useField({ ...props, type: 'checkbox' });
  const hasError = meta.touched && meta.error;
  const { direction } = useContext(LanguageContext);
  const isRtl = direction === 'rtl';

  const handleToggle = () => {
    if (!props.disabled) {
      helpers.setValue(!field.value);
    }
  };

  return (
    <div className={`mb-4 ${className}`}>
      <div className="flex items-center justify-between">
        {label && (
          <label
            htmlFor={props.id || props.name}
            className={`text-sm font-medium ${props.disabled ? 'text-gray-400' : 'text-gray-700'}`}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1 rtl-ml-0 rtl-mr-1">*</span>}
          </label>
        )}

        {/* Custom Switch Implementation */}
        <div
          onClick={handleToggle}
          className={`
            relative w-11 h-6 rounded-full cursor-pointer
            ${field.value ? 'bg-indigo-600' : 'bg-gray-200'}
            ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          {/* Toggle Circle */}
          <div
            className="absolute w-5 h-5 bg-white rounded-full shadow transition-all duration-200 ease-in-out"
            style={{
              top: '2px',
              left: isRtl
                ? (field.value ? 'calc(100% - 20px - 2px)' : '2px')
                : (field.value ? '2px' : 'calc(100% - 20px - 2px)')
            }}
          />
        </div>
      </div>

      <div className="min-h-[20px] mt-1">
        {hasError ? (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600"
          >
            {meta.error}
          </motion.p>
        ) : helperText ? (
          <p className="text-xs text-gray-500">{helperText}</p>
        ) : null}
      </div>
    </div>
  );
};

export default FormSwitch;
