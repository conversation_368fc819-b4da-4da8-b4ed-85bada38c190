import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import ProductFilters from "./ProductFilters";
import PreviewModal from "./PreviewModal";
import { FaEdit, FaTrash, FaClipboardList, FaEye, FaPlus, FaBox } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const ProductIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [previewProduct, setPreviewProduct] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: '',
    status: true,
    category: '',
    brand: ''
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: productsData,
    isLoading,
    isError: productsError,
    refetch,
  } = fetchData("admin/products", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions
  });

  const productList = productsData?.data?.data || [];
  const paginationInfo = {
    currentPage: productsData?.data?.current_page || 1,
    perPage: productsData?.data?.per_page || itemsPerPage,
    totalItems: productsData?.data?.total_items || 0,
    totalPages: productsData?.data?.total_pages || 1,
  };



  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditProduct = (product) => {
    navigate(`/products/edit/${product.id}`);
  };

  const handleDeleteClick = (product) => {
    setSelectedProduct(product);
  };

  const handlePreviewClick = (product) => {
    setPreviewProduct(product);
  };

  const handleDeleteConfirm = () => {
    if (!selectedProduct) return;

    setDeleteLoading(true);
    deleteMutation.mutate(
      { endpoint: `admin/products/${selectedProduct.id}` },
      {
        onSuccess: () => {
          toast.success("Product deleted successfully");
          setSelectedProduct(null);
          refetch();
        },
        onError: (error) => {
          console.error("Product deletion failed:", error?.response?.data || error.message);
          toast.error("Product deletion failed: " + (error?.response?.data?.message || error.message));
        },
        onSettled: () => setDeleteLoading(false),
      }
    );
  };

  const columns = [
    {
      key: "featured_image",
      header: "Image",
      render: (product) => (
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
          {product.featured_image ? (
            <img
              src={product.featured_image}
              alt={product.title_en}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <FaBox className="text-gray-400" />
            </div>
          )}
        </div>
      ),
    },
    {
      key: "title_en",
      header: "Product Name",
      render: (product) => (
        <div>
          <div className="font-medium text-gray-900">
            {product.title_en || "Untitled Product"}
          </div>
          {product.title_ar && (
            <div className="text-sm text-gray-500 mt-1" dir="rtl">{product.title_ar}</div>
          )}
          {/* Display SKU - prioritize system_sku, fallback to sku */}
          {(product.system_sku || product.sku) && (
            <div className="text-xs text-gray-400 mt-1 font-mono">
              SKU: {product.system_sku || product.sku}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "category",
      header: "Category",
      render: (product) => (
        <span className="text-sm">{product.category?.name || 'N/A'}</span>
      ),
    },
    {
      key: "price",
      header: "Price",
      render: (product) => (
        <div>
          <div className="font-medium">AED {product.regular_price}</div>
          {product.offer_price && (
            <div className="text-sm text-green-600">Offer: AED {product.offer_price}</div>
          )}
        </div>
      ),
    },
    {
      key: "stock",
      header: "Stock",
      render: (product) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          product.stock_quantity > 10
            ? 'bg-green-100 text-green-800'
            : product.stock_quantity > 0
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {product.stock_quantity || 0}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (product) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          product.status === 'active'
            ? 'bg-green-100 text-green-800'
            : product.status === 'draft'
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {product.status}
        </span>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      render: (product) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(product)}
            className="text-blue-600 hover:text-blue-800"
          >
            <FaEye />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditProduct(product)}
            className="text-green-600 hover:text-green-800"
          >
            <FaEdit />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(product)}
            className="text-red-600 hover:text-red-800"
          >
            <FaTrash />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Product Management"
          icon={<FaClipboardList className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              className="ml-2"
              onClick={() => navigate("/products/add")}
            >
              <FaPlus className="mr-2" /> Add Product
            </Button>
          }
        >
          <ProductFilters onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={productList}
            emptyMessage={t("products.emptyMessage") || "No products found"}
          />

          <PaginationInfo
            currentPage={paginationInfo.currentPage}
            totalPages={paginationInfo.totalPages}
            totalItems={paginationInfo.totalItems}
            itemsPerPage={paginationInfo.perPage}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Modal */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Confirm Delete
            </h3>
            <p className="text-sm text-gray-500 mb-6">
              Are you sure you want to delete "{selectedProduct.title_en}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                onClick={() => setSelectedProduct(null)}
                disabled={deleteLoading}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteConfirm}
                loading={deleteLoading}
                disabled={deleteLoading}
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewProduct && (
        <PreviewModal
          product={previewProduct}
          onClose={() => setPreviewProduct(null)}
        />
      )}
    </div>
  );
};

export default ProductIndex;
