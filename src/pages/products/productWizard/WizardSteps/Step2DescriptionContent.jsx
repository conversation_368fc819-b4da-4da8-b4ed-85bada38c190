import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion } from "framer-motion";
import { FaFileAlt, FaGlobe, FaList } from "react-icons/fa";
import { FormInput, FormTextarea, RichTextEditor } from "@/components/ui/form";
import Card from "@/components/ui/Card";

const Step2DescriptionContent = ({ formData, onChange, validationErrors, setValidationErrors }) => {
  const [layoutMode, setLayoutMode] = useState("horizontal"); // horizontal or vertical

  const initialValues = {
    short_description_en: formData.short_description_en || "",
    short_description_ar: formData.short_description_ar || "",
    description_en: formData.description_en || "",
    description_ar: formData.description_ar || "",
    key_ingredients_en: formData.key_ingredients_en || "",
    key_ingredients_ar: formData.key_ingredients_ar || "",
    usage_instructions_en: formData.usage_instructions_en || "",
    usage_instructions_ar: formData.usage_instructions_ar || "",
    features_en: formData.features_en || "",
    features_ar: formData.features_ar || "",
    specifications_en: formData.specifications_en || "",
    specifications_ar: formData.specifications_ar || ""
  };

  const validationSchema = Yup.object({
    short_description_en: Yup.string().required("English short description is required"),
    description_en: Yup.string().required("English description is required"),
  });

  const handleSubmit = (values) => {
    onChange(values);
    setValidationErrors(prev => ({ ...prev, 2: null }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title="Description & Content"
        icon={<FaFileAlt className="text-indigo-600" />}
        action={
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Layout:</span>
            <button
              onClick={() => setLayoutMode(layoutMode === "horizontal" ? "vertical" : "horizontal")}
              className="flex items-center gap-2 px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <FaGlobe className="w-4 h-4" />
              {layoutMode === "horizontal" ? "Horizontal" : "Vertical"}
            </button>
          </div>
        }
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Short Descriptions */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Short Descriptions
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <FormTextarea
                      name="short_description_en"
                      label="Short Description (English)"
                      placeholder="Enter a brief product description in English (max 200 characters)"
                      required
                      rows={3}
                      maxLength={200}
                    />
                    <FormTextarea
                      name="short_description_ar"
                      label="Short Description (Arabic)"
                      placeholder="أدخل وصفاً مختصراً للمنتج بالعربية (الحد الأقصى 200 حرف)"
                      dir="rtl"
                      rows={3}
                      maxLength={200}
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FormTextarea
                      name="short_description_en"
                      label="Short Description (English)"
                      placeholder="Enter a brief product description in English (max 200 characters)"
                      required
                      rows={3}
                      maxLength={200}
                    />
                    <FormTextarea
                      name="short_description_ar"
                      label="Short Description (Arabic)"
                      placeholder="أدخل وصفاً مختصراً للمنتج بالعربية (الحد الأقصى 200 حرف)"
                      dir="rtl"
                      rows={3}
                      maxLength={200}
                    />
                  </div>
                )}
              </div>

              {/* Full Descriptions */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Full Descriptions
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Description (English) <span className="text-red-500">*</span>
                      </label>
                      <RichTextEditor
                        value={values.description_en}
                        onChange={(value) => setFieldValue('description_en', value)}
                        placeholder="Enter detailed product description in English"
                        height={200}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Description (Arabic)
                      </label>
                      <RichTextEditor
                        value={values.description_ar}
                        onChange={(value) => setFieldValue('description_ar', value)}
                        placeholder="أدخل وصفاً مفصلاً للمنتج بالعربية"
                        height={200}
                        dir="rtl"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Description (English) <span className="text-red-500">*</span>
                      </label>
                      <RichTextEditor
                        value={values.description_en}
                        onChange={(value) => setFieldValue('description_en', value)}
                        placeholder="Enter detailed product description in English"
                        height={200}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Description (Arabic)
                      </label>
                      <RichTextEditor
                        value={values.description_ar}
                        onChange={(value) => setFieldValue('description_ar', value)}
                        placeholder="أدخل وصفاً مفصلاً للمنتج بالعربية"
                        height={200}
                        dir="rtl"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Key Ingredients */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Key Ingredients / Components
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <FormTextarea
                      name="key_ingredients_en"
                      label="Key Ingredients (English)"
                      placeholder="List main ingredients or components in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="key_ingredients_ar"
                      label="Key Ingredients (Arabic)"
                      placeholder="اذكر المكونات أو العناصر الرئيسية بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FormTextarea
                      name="key_ingredients_en"
                      label="Key Ingredients (English)"
                      placeholder="List main ingredients or components in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="key_ingredients_ar"
                      label="Key Ingredients (Arabic)"
                      placeholder="اذكر المكونات أو العناصر الرئيسية بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                  </div>
                )}
              </div>

              {/* Usage Instructions */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Usage Instructions
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <FormTextarea
                      name="usage_instructions_en"
                      label="Usage Instructions (English)"
                      placeholder="Provide usage instructions in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="usage_instructions_ar"
                      label="Usage Instructions (Arabic)"
                      placeholder="قدم تعليمات الاستخدام بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FormTextarea
                      name="usage_instructions_en"
                      label="Usage Instructions (English)"
                      placeholder="Provide usage instructions in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="usage_instructions_ar"
                      label="Usage Instructions (Arabic)"
                      placeholder="قدم تعليمات الاستخدام بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                  </div>
                )}
              </div>

              {/* Features & Specifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Features & Specifications
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div className="space-y-4">
                      <FormTextarea
                        name="features_en"
                        label="Key Features (English)"
                        placeholder="List key product features in English"
                        rows={4}
                      />
                      <FormTextarea
                        name="specifications_en"
                        label="Technical Specifications (English)"
                        placeholder="Provide technical specifications in English"
                        rows={4}
                      />
                    </div>
                    <div className="space-y-4">
                      <FormTextarea
                        name="features_ar"
                        label="Key Features (Arabic)"
                        placeholder="اذكر الميزات الرئيسية للمنتج بالعربية"
                        dir="rtl"
                        rows={4}
                      />
                      <FormTextarea
                        name="specifications_ar"
                        label="Technical Specifications (Arabic)"
                        placeholder="قدم المواصفات التقنية بالعربية"
                        dir="rtl"
                        rows={4}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FormTextarea
                      name="features_en"
                      label="Key Features (English)"
                      placeholder="List key product features in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="features_ar"
                      label="Key Features (Arabic)"
                      placeholder="اذكر الميزات الرئيسية للمنتج بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                    <FormTextarea
                      name="specifications_en"
                      label="Technical Specifications (English)"
                      placeholder="Provide technical specifications in English"
                      rows={4}
                    />
                    <FormTextarea
                      name="specifications_ar"
                      label="Technical Specifications (Arabic)"
                      placeholder="قدم المواصفات التقنية بالعربية"
                      dir="rtl"
                      rows={4}
                    />
                  </div>
                )}
              </div>

              {/* Validation Errors */}
              {validationErrors[2] && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{validationErrors[2]}</p>
                </div>
              )}

              {/* Auto-save indicator */}
              <div className="text-center">
                <p className="text-sm text-gray-500">
                  Changes are automatically saved as you type
                </p>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </motion.div>
  );
};

export default Step2DescriptionContent;
