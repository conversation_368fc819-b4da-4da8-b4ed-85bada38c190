import Step1BasicInformation from "./Step1BasicInformation";
import Step2DescriptionContent from "./Step2DescriptionContent";
import Step3MediaManagement from "./Step3MediaManagement";
import Step4PricingInventory from "./Step4PricingInventory";
import Step5ProductVariants from "./Step5ProductVariants";
import Step6FulfillmentLogistics from "./Step6FulfillmentLogistics";
import Step7ComplianceCertifications from "./Step7ComplianceCertifications";
import Step8SEOOptimization from "./Step8SEOOptimization";
import Step9FAQsManagement from "./Step9FAQsManagement";
import Step10ReviewSubmit from "./Step10ReviewSubmit";

const WizardSteps = ({ 
  currentStep, 
  formData, 
  onChange, 
  validationErrors, 
  setValidationErrors 
}) => {
  const stepProps = {
    formData,
    onChange,
    validationErrors,
    setValidationErrors
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <Step1BasicInformation {...stepProps} />;
      case 2:
        return <Step2DescriptionContent {...stepProps} />;
      case 3:
        return <Step3MediaManagement {...stepProps} />;
      case 4:
        return <Step4PricingInventory {...stepProps} />;
      case 5:
        return <Step5ProductVariants {...stepProps} />;
      case 6:
        return <Step6FulfillmentLogistics {...stepProps} />;
      case 7:
        return <Step7ComplianceCertifications {...stepProps} />;
      case 8:
        return <Step8SEOOptimization {...stepProps} />;
      case 9:
        return <Step9FAQsManagement {...stepProps} />;
      case 10:
        return <Step10ReviewSubmit {...stepProps} />;
      default:
        return <Step1BasicInformation {...stepProps} />;
    }
  };

  return (
    <div className="min-h-[600px]">
      {renderStep()}
    </div>
  );
};

export default WizardSteps;
