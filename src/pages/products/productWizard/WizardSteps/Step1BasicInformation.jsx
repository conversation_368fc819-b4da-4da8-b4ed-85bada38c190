import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion } from "framer-motion";
import { FaInfoCircle, FaGlobe } from "react-icons/fa";
import { FormInput, FormSelect, FormSwitch } from "@/components/ui/form";
import Card from "@/components/ui/Card";
import { fetchData } from "@/hooks/useApi";

const Step1BasicInformation = ({ formData, onChange, validationErrors, setValidationErrors }) => {
  const [layoutMode, setLayoutMode] = useState("horizontal"); // horizontal or vertical
  
  // Fetch categories, brands, and other dropdown data
  const { data: categoriesData, isLoading: categoriesLoading } = fetchData("admin/categories", { pagination: false });
  const { data: brandsData, isLoading: brandsLoading } = fetchData("admin/brands", { pagination: false });
  const { data: classesData, isLoading: classesLoading } = fetchData("admin/product-classes", { pagination: false });

  const categories = categoriesData?.data || [];
  const brands = brandsData?.data || [];
  const classes = classesData?.data || [];

  const initialValues = {
    title_en: formData.title_en || "",
    title_ar: formData.title_ar || "",
    short_name: formData.short_name || "",
    vendor_sku: formData.vendor_sku || "",
    system_sku: formData.system_sku || "",
    barcode: formData.barcode || "",
    model_number: formData.model_number || "",
    brand_id: formData.brand_id || "",
    category_id: formData.category_id || "",
    subcategory_id: formData.subcategory_id || "",
    class_id: formData.class_id || "",
    subclass_id: formData.subclass_id || "",
    has_variants: formData.has_variants || false,
    status: formData.status || "draft"
  };

  const validationSchema = Yup.object({
    title_en: Yup.string().required("English title is required"),
    title_ar: Yup.string(),
    short_name: Yup.string().required("Short name is required"),
    vendor_sku: Yup.string().required("Vendor SKU is required"),
    brand_id: Yup.string().required("Brand is required"),
    category_id: Yup.string().required("Category is required"),
    class_id: Yup.string().required("Product class is required"),
  });

  const categoryOptions = categories.map(cat => ({
    label: cat.name_en || cat.name,
    value: cat.id
  }));

  const brandOptions = brands.map(brand => ({
    label: brand.name_en || brand.name,
    value: brand.id
  }));

  const classOptions = classes.map(cls => ({
    label: cls.name_en || cls.name,
    value: cls.id
  }));

  const statusOptions = [
    { label: "Draft", value: "draft" },
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" }
  ];

  const handleSubmit = (values) => {
    // Generate system SKU if not provided
    if (!values.system_sku && values.vendor_sku) {
      values.system_sku = `SYS-${values.vendor_sku}-${Date.now()}`;
    }
    
    onChange(values);
    setValidationErrors(prev => ({ ...prev, 1: null }));
  };

  const generateSystemSKU = (vendorSku) => {
    if (!vendorSku) return "";
    return `SYS-${vendorSku}-${Date.now()}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title="Basic Product Information"
        icon={<FaInfoCircle className="text-indigo-600" />}
        action={
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Layout:</span>
            <button
              onClick={() => setLayoutMode(layoutMode === "horizontal" ? "vertical" : "horizontal")}
              className="flex items-center gap-2 px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <FaGlobe className="w-4 h-4" />
              {layoutMode === "horizontal" ? "Horizontal" : "Vertical"}
            </button>
          </div>
        }
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* Product Titles */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Product Titles
                </h3>
                
                {layoutMode === "horizontal" ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <FormInput
                      name="title_en"
                      label="Product Title (English)"
                      placeholder="Enter product title in English"
                      required
                      onChange={(e) => {
                        setFieldValue('title_en', e.target.value);
                        // Auto-generate short name from English title
                        if (!values.short_name) {
                          setFieldValue('short_name', e.target.value.substring(0, 50));
                        }
                      }}
                    />
                    <FormInput
                      name="title_ar"
                      label="Product Title (Arabic)"
                      placeholder="أدخل عنوان المنتج بالعربية"
                      dir="rtl"
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FormInput
                      name="title_en"
                      label="Product Title (English)"
                      placeholder="Enter product title in English"
                      required
                      onChange={(e) => {
                        setFieldValue('title_en', e.target.value);
                        if (!values.short_name) {
                          setFieldValue('short_name', e.target.value.substring(0, 50));
                        }
                      }}
                    />
                    <FormInput
                      name="title_ar"
                      label="Product Title (Arabic)"
                      placeholder="أدخل عنوان المنتج بالعربية"
                      dir="rtl"
                    />
                  </div>
                )}

                <FormInput
                  name="short_name"
                  label="Short Name"
                  placeholder="Enter short product name (max 50 characters)"
                  required
                  maxLength={50}
                />
              </div>

              {/* Product Identification */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Product Identification
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormInput
                    name="vendor_sku"
                    label="Vendor SKU"
                    placeholder="Enter your product SKU"
                    required
                    onChange={(e) => {
                      setFieldValue('vendor_sku', e.target.value);
                      // Auto-generate system SKU
                      if (e.target.value) {
                        setFieldValue('system_sku', generateSystemSKU(e.target.value));
                      }
                    }}
                  />
                  <FormInput
                    name="system_sku"
                    label="System SKU"
                    placeholder="Auto-generated system SKU"
                    disabled
                    className="bg-gray-50"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormInput
                    name="barcode"
                    label="Barcode"
                    placeholder="Enter product barcode"
                  />
                  <FormInput
                    name="model_number"
                    label="Model Number"
                    placeholder="Enter model number"
                  />
                </div>
              </div>

              {/* Product Classification */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Product Classification
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormSelect
                    name="brand_id"
                    label="Brand"
                    options={brandOptions}
                    required
                    loading={brandsLoading}
                    placeholder="Select a brand"
                  />
                  <FormSelect
                    name="category_id"
                    label="Category"
                    options={categoryOptions}
                    required
                    loading={categoriesLoading}
                    placeholder="Select a category"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormSelect
                    name="class_id"
                    label="Product Class"
                    options={classOptions}
                    required
                    loading={classesLoading}
                    placeholder="Select a product class"
                  />
                  <FormSelect
                    name="subclass_id"
                    label="Product Subclass"
                    options={[]} // Will be populated based on class selection
                    placeholder="Select a subclass"
                    disabled={!values.class_id}
                  />
                </div>
              </div>

              {/* Product Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Product Configuration
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <FormSwitch
                      name="has_variants"
                      label="Product has variants"
                      description="Enable if this product has variations like size, color, etc."
                    />
                  </div>
                  
                  <FormSelect
                    name="status"
                    label="Status"
                    options={statusOptions}
                    required
                  />
                </div>
              </div>

              {/* Validation Errors */}
              {validationErrors[1] && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{validationErrors[1]}</p>
                </div>
              )}

              {/* Auto-save indicator */}
              <div className="text-center">
                <p className="text-sm text-gray-500">
                  Changes are automatically saved as you type
                </p>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </motion.div>
  );
};

export default Step1BasicInformation;
