import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion } from "framer-motion";
import { FaDollarSign, FaWarehouse, FaCalendar, FaPercent } from "react-icons/fa";
import { FormInput, FormSelect, FormDatePicker, FormSwitch } from "@/components/ui/form";
import Card from "@/components/ui/Card";

const Step4PricingInventory = ({ formData, onChange, validationErrors, setValidationErrors }) => {
  const initialValues = {
    regular_price: formData.regular_price || "",
    offer_price: formData.offer_price || "",
    offer_start_date: formData.offer_start_date || "",
    offer_end_date: formData.offer_end_date || "",
    cost_price: formData.cost_price || "",
    vat_rate: formData.vat_rate || "5", // Default UAE VAT rate
    vat_inclusive: formData.vat_inclusive || false,
    commission_rate: formData.commission_rate || "",
    stock_quantity: formData.stock_quantity || "",
    reserved_stock: formData.reserved_stock || "0",
    low_stock_threshold: formData.low_stock_threshold || "10",
    stock_location: formData.stock_location || "",
    track_inventory: formData.track_inventory || true,
    allow_backorders: formData.allow_backorders || false,
    weight: formData.weight || "",
    dimensions_length: formData.dimensions_length || "",
    dimensions_width: formData.dimensions_width || "",
    dimensions_height: formData.dimensions_height || "",
    dimension_unit: formData.dimension_unit || "cm"
  };

  const validationSchema = Yup.object({
    regular_price: Yup.number()
      .required("Regular price is required")
      .min(0.01, "Price must be greater than 0"),
    offer_price: Yup.number()
      .min(0, "Offer price cannot be negative")
      .test('offer-less-than-regular', 'Offer price must be less than regular price', function(value) {
        const { regular_price } = this.parent;
        if (value && regular_price) {
          return parseFloat(value) < parseFloat(regular_price);
        }
        return true;
      }),
    stock_quantity: Yup.number()
      .required("Stock quantity is required")
      .min(0, "Stock quantity cannot be negative"),
    vat_rate: Yup.number()
      .required("VAT rate is required")
      .min(0, "VAT rate cannot be negative")
      .max(100, "VAT rate cannot exceed 100%"),
  });

  const vatRateOptions = [
    { label: "0% (Zero-rated)", value: "0" },
    { label: "5% (Standard UAE VAT)", value: "5" },
    { label: "Custom Rate", value: "custom" }
  ];

  const dimensionUnitOptions = [
    { label: "Centimeters (cm)", value: "cm" },
    { label: "Meters (m)", value: "m" },
    { label: "Inches (in)", value: "in" },
    { label: "Feet (ft)", value: "ft" }
  ];

  const handleSubmit = (values) => {
    onChange(values);
    setValidationErrors(prev => ({ ...prev, 4: null }));
  };

  const calculateVATAmount = (price, vatRate, vatInclusive) => {
    if (!price || !vatRate) return 0;
    const priceNum = parseFloat(price);
    const vatRateNum = parseFloat(vatRate);
    
    if (vatInclusive) {
      return (priceNum * vatRateNum) / (100 + vatRateNum);
    } else {
      return (priceNum * vatRateNum) / 100;
    }
  };

  const calculateFinalPrice = (price, vatRate, vatInclusive) => {
    if (!price) return 0;
    const priceNum = parseFloat(price);
    const vatAmount = calculateVATAmount(price, vatRate, vatInclusive);
    
    return vatInclusive ? priceNum : priceNum + vatAmount;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title="Pricing & Inventory"
        icon={<FaDollarSign className="text-indigo-600" />}
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Pricing Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 flex items-center gap-2">
                  <FaDollarSign className="text-green-600" />
                  Pricing Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormInput
                    name="regular_price"
                    label="Regular Price (AED)"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    required
                  />
                  <FormInput
                    name="offer_price"
                    label="Offer Price (AED)"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    description="Leave empty if no offer price"
                  />
                </div>

                {values.offer_price && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormDatePicker
                      name="offer_start_date"
                      label="Offer Start Date"
                      placeholder="Select start date"
                    />
                    <FormDatePicker
                      name="offer_end_date"
                      label="Offer End Date"
                      placeholder="Select end date"
                    />
                  </div>
                )}

                <FormInput
                  name="cost_price"
                  label="Cost Price (AED)"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  description="Your cost for this product (for profit calculation)"
                />
              </div>

              {/* VAT Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 flex items-center gap-2">
                  <FaPercent className="text-blue-600" />
                  VAT Configuration
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormSelect
                    name="vat_rate"
                    label="VAT Rate"
                    options={vatRateOptions}
                    required
                  />
                  {values.vat_rate === "custom" && (
                    <FormInput
                      name="vat_rate"
                      label="Custom VAT Rate (%)"
                      type="number"
                      step="0.01"
                      placeholder="Enter custom VAT rate"
                      onChange={(e) => setFieldValue('vat_rate', e.target.value)}
                    />
                  )}
                </div>

                <FormSwitch
                  name="vat_inclusive"
                  label="VAT Inclusive Pricing"
                  description="Check if the entered price already includes VAT"
                />

                {/* VAT Calculation Display */}
                {values.regular_price && values.vat_rate && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Price Breakdown</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Base Price:</span>
                        <span>AED {values.vat_inclusive ? 
                          (parseFloat(values.regular_price) - calculateVATAmount(values.regular_price, values.vat_rate, true)).toFixed(2) :
                          parseFloat(values.regular_price).toFixed(2)
                        }</span>
                      </div>
                      <div className="flex justify-between">
                        <span>VAT ({values.vat_rate}%):</span>
                        <span>AED {calculateVATAmount(values.regular_price, values.vat_rate, values.vat_inclusive).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium border-t pt-1">
                        <span>Final Price:</span>
                        <span>AED {calculateFinalPrice(values.regular_price, values.vat_rate, values.vat_inclusive).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Commission Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Commission Information
                </h3>
                
                <FormInput
                  name="commission_rate"
                  label="Platform Commission Rate (%)"
                  type="number"
                  step="0.01"
                  placeholder="Platform commission rate"
                  description="Commission rate charged by the platform"
                  disabled
                />

                {values.regular_price && values.commission_rate && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Commission Estimate</h4>
                    <div className="text-sm">
                      <div className="flex justify-between">
                        <span>Sale Price:</span>
                        <span>AED {parseFloat(values.regular_price).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Commission ({values.commission_rate}%):</span>
                        <span>AED {((parseFloat(values.regular_price) * parseFloat(values.commission_rate)) / 100).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium border-t pt-1">
                        <span>Your Earnings:</span>
                        <span>AED {(parseFloat(values.regular_price) - ((parseFloat(values.regular_price) * parseFloat(values.commission_rate)) / 100)).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Inventory Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 flex items-center gap-2">
                  <FaWarehouse className="text-purple-600" />
                  Inventory Management
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormInput
                    name="stock_quantity"
                    label="Stock Quantity"
                    type="number"
                    placeholder="0"
                    required
                  />
                  <FormInput
                    name="reserved_stock"
                    label="Reserved Stock"
                    type="number"
                    placeholder="0"
                    description="Stock reserved for pending orders"
                  />
                  <FormInput
                    name="low_stock_threshold"
                    label="Low Stock Alert"
                    type="number"
                    placeholder="10"
                    description="Alert when stock falls below this number"
                  />
                </div>

                <FormInput
                  name="stock_location"
                  label="Stock Location"
                  placeholder="Warehouse location or identifier"
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormSwitch
                    name="track_inventory"
                    label="Track Inventory"
                    description="Enable inventory tracking for this product"
                  />
                  <FormSwitch
                    name="allow_backorders"
                    label="Allow Backorders"
                    description="Allow orders when out of stock"
                  />
                </div>
              </div>

              {/* Dimensions & Weight */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Dimensions & Weight
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <FormInput
                    name="dimensions_length"
                    label="Length"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                  />
                  <FormInput
                    name="dimensions_width"
                    label="Width"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                  />
                  <FormInput
                    name="dimensions_height"
                    label="Height"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                  />
                  <FormSelect
                    name="dimension_unit"
                    label="Unit"
                    options={dimensionUnitOptions}
                  />
                </div>

                <FormInput
                  name="weight"
                  label="Weight (kg)"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  description="Product weight for shipping calculations"
                />
              </div>

              {/* Validation Errors */}
              {validationErrors[4] && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{validationErrors[4]}</p>
                </div>
              )}

              {/* Auto-save indicator */}
              <div className="text-center">
                <p className="text-sm text-gray-500">
                  Changes are automatically saved as you type
                </p>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </motion.div>
  );
};

export default Step4PricingInventory;
