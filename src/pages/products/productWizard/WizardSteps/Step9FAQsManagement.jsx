import { motion } from "framer-motion";
import { FaQuestionCircle } from "react-icons/fa";
import Card from "@/components/ui/Card";

const Step9FAQsManagement = ({ formData, onChange, validationErrors, setValidationErrors }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title="FAQs Management"
        icon={<FaQuestionCircle className="text-indigo-600" />}
      >
        <div className="text-center py-12">
          <FaQuestionCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">FAQs Management</h3>
          <p className="text-gray-600 mb-4">
            Add frequently asked questions and answers about your product.
          </p>
          <p className="text-sm text-gray-500">
            This step will be implemented in the next phase of development.
          </p>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step9FAQsManagement;
