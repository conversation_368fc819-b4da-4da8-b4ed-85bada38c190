import { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { motion, AnimatePresence } from "framer-motion";
import { FaImages, FaGlobe, FaTrash, FaEdit, FaEye, FaPlus, FaStar } from "react-icons/fa";
import { FormInput, FormTextarea } from "@/components/ui/form";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import ImageUpload from "@/components/ui/ImageUpload";
import Modal from "@/components/ui/Modal";

const Step3MediaManagement = ({ formData, onChange, validationErrors, setValidationErrors }) => {
  const [layoutMode, setLayoutMode] = useState("horizontal");
  const [selectedImage, setSelectedImage] = useState(null);
  const [editingImage, setEditingImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);

  const initialValues = {
    featured_image: formData.featured_image || "",
    gallery: formData.gallery || [],
    videos: formData.videos || []
  };

  const validationSchema = Yup.object({
    featured_image: Yup.string().required("Featured image is required"),
  });

  const handleSubmit = (values) => {
    onChange(values);
    setValidationErrors(prev => ({ ...prev, 3: null }));
  };

  const handleFeaturedImageUpload = (url, setFieldValue) => {
    setFieldValue('featured_image', url);
  };

  const handleGalleryImageUpload = (url, setFieldValue, values) => {
    const newImage = {
      id: Date.now(),
      url: url,
      title_en: "",
      title_ar: "",
      alt_text_en: "",
      alt_text_ar: "",
      is_primary: false,
      sort_order: values.gallery.length
    };
    
    const updatedGallery = [...values.gallery, newImage];
    setFieldValue('gallery', updatedGallery);
  };

  const handleImageEdit = (image, setFieldValue, values) => {
    const updatedGallery = values.gallery.map(img => 
      img.id === image.id ? image : img
    );
    setFieldValue('gallery', updatedGallery);
    setEditingImage(null);
  };

  const handleImageDelete = (imageId, setFieldValue, values) => {
    const updatedGallery = values.gallery.filter(img => img.id !== imageId);
    setFieldValue('gallery', updatedGallery);
  };

  const handleSetPrimary = (imageId, setFieldValue, values) => {
    const updatedGallery = values.gallery.map(img => ({
      ...img,
      is_primary: img.id === imageId
    }));
    setFieldValue('gallery', updatedGallery);
  };

  const handleReorderImages = (dragIndex, hoverIndex, setFieldValue, values) => {
    const draggedImage = values.gallery[dragIndex];
    const updatedGallery = [...values.gallery];
    updatedGallery.splice(dragIndex, 1);
    updatedGallery.splice(hoverIndex, 0, draggedImage);
    
    // Update sort order
    const reorderedGallery = updatedGallery.map((img, index) => ({
      ...img,
      sort_order: index
    }));
    
    setFieldValue('gallery', reorderedGallery);
  };

  const ImageEditModal = ({ image, onSave, onClose }) => {
    const [editData, setEditData] = useState(image);

    return (
      <Modal isOpen={true} onClose={onClose} size="lg">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Image Details</h3>
          
          <div className="space-y-4">
            <div className="w-32 h-32 rounded-lg overflow-hidden border border-gray-200 mx-auto">
              <img
                src={image.url}
                alt={image.title_en}
                className="w-full h-full object-cover"
              />
            </div>

            {layoutMode === "horizontal" ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormInput
                  label="Title (English)"
                  value={editData.title_en}
                  onChange={(e) => setEditData({...editData, title_en: e.target.value})}
                  placeholder="Enter image title in English"
                />
                <FormInput
                  label="Title (Arabic)"
                  value={editData.title_ar}
                  onChange={(e) => setEditData({...editData, title_ar: e.target.value})}
                  placeholder="أدخل عنوان الصورة بالعربية"
                  dir="rtl"
                />
              </div>
            ) : (
              <div className="space-y-4">
                <FormInput
                  label="Title (English)"
                  value={editData.title_en}
                  onChange={(e) => setEditData({...editData, title_en: e.target.value})}
                  placeholder="Enter image title in English"
                />
                <FormInput
                  label="Title (Arabic)"
                  value={editData.title_ar}
                  onChange={(e) => setEditData({...editData, title_ar: e.target.value})}
                  placeholder="أدخل عنوان الصورة بالعربية"
                  dir="rtl"
                />
              </div>
            )}

            {layoutMode === "horizontal" ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormTextarea
                  label="Alt Text (English)"
                  value={editData.alt_text_en}
                  onChange={(e) => setEditData({...editData, alt_text_en: e.target.value})}
                  placeholder="Enter alt text for accessibility"
                  rows={3}
                />
                <FormTextarea
                  label="Alt Text (Arabic)"
                  value={editData.alt_text_ar}
                  onChange={(e) => setEditData({...editData, alt_text_ar: e.target.value})}
                  placeholder="أدخل النص البديل لإمكانية الوصول"
                  dir="rtl"
                  rows={3}
                />
              </div>
            ) : (
              <div className="space-y-4">
                <FormTextarea
                  label="Alt Text (English)"
                  value={editData.alt_text_en}
                  onChange={(e) => setEditData({...editData, alt_text_en: e.target.value})}
                  placeholder="Enter alt text for accessibility"
                  rows={3}
                />
                <FormTextarea
                  label="Alt Text (Arabic)"
                  value={editData.alt_text_ar}
                  onChange={(e) => setEditData({...editData, alt_text_ar: e.target.value})}
                  placeholder="أدخل النص البديل لإمكانية الوصول"
                  dir="rtl"
                  rows={3}
                />
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="primary" onClick={() => onSave(editData)}>
              Save Changes
            </Button>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title="Media Management"
        icon={<FaImages className="text-indigo-600" />}
        action={
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Layout:</span>
            <button
              onClick={() => setLayoutMode(layoutMode === "horizontal" ? "vertical" : "horizontal")}
              className="flex items-center gap-2 px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <FaGlobe className="w-4 h-4" />
              {layoutMode === "horizontal" ? "Horizontal" : "Vertical"}
            </button>
          </div>
        }
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched }) => (
            <Form className="space-y-6">
              {/* Featured Image */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Featured Image <span className="text-red-500">*</span>
                </h3>
                
                <div className="max-w-md">
                  <ImageUpload
                    value={values.featured_image}
                    onUploadSuccess={(url) => handleFeaturedImageUpload(url, setFieldValue)}
                    className="w-full h-64"
                    accept="image/*"
                    maxSize={5} // 5MB
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    Upload a high-quality image that represents your product. Recommended size: 800x800px or larger.
                  </p>
                </div>
              </div>

              {/* Gallery Images */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 flex-1">
                    Gallery Images
                  </h3>
                  <span className="text-sm text-gray-500">
                    {values.gallery.length}/10 images
                  </span>
                </div>

                {/* Upload New Image */}
                {values.gallery.length < 10 && (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <ImageUpload
                      onUploadSuccess={(url) => handleGalleryImageUpload(url, setFieldValue, values)}
                      className="w-full h-32"
                      accept="image/*"
                      maxSize={5}
                      showPreview={false}
                    />
                    <p className="text-sm text-gray-500 text-center mt-2">
                      Click to upload additional product images (max 10)
                    </p>
                  </div>
                )}

                {/* Gallery Grid */}
                {values.gallery.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <AnimatePresence>
                      {values.gallery
                        .sort((a, b) => a.sort_order - b.sort_order)
                        .map((image, index) => (
                          <motion.div
                            key={image.id}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            className="relative group"
                          >
                            <div className="aspect-square rounded-lg overflow-hidden border border-gray-200 bg-gray-100">
                              <img
                                src={image.url}
                                alt={image.title_en || `Gallery image ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                              
                              {/* Primary indicator */}
                              {image.is_primary && (
                                <div className="absolute top-2 left-2">
                                  <FaStar className="w-4 h-4 text-yellow-500" />
                                </div>
                              )}

                              {/* Action buttons */}
                              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setSelectedImage(image)}
                                  className="bg-white text-gray-700 hover:bg-gray-100"
                                >
                                  <FaEye className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingImage(image)}
                                  className="bg-white text-gray-700 hover:bg-gray-100"
                                >
                                  <FaEdit className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleSetPrimary(image.id, setFieldValue, values)}
                                  className="bg-white text-gray-700 hover:bg-gray-100"
                                  disabled={image.is_primary}
                                >
                                  <FaStar className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleImageDelete(image.id, setFieldValue, values)}
                                  className="bg-white text-red-600 hover:bg-red-50"
                                >
                                  <FaTrash className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Image info */}
                            <div className="mt-2 text-xs text-gray-600">
                              <div className="truncate">
                                {image.title_en || `Image ${index + 1}`}
                              </div>
                              {image.is_primary && (
                                <div className="text-yellow-600 font-medium">Primary</div>
                              )}
                            </div>
                          </motion.div>
                        ))}
                    </AnimatePresence>
                  </div>
                )}

                {values.gallery.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FaImages className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No gallery images uploaded yet</p>
                    <p className="text-sm">Upload images to showcase your product from different angles</p>
                  </div>
                )}
              </div>

              {/* Video URLs */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Product Videos (Optional)
                </h3>
                
                <div className="space-y-3">
                  {values.videos.map((video, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <FormInput
                        label={`Video URL ${index + 1}`}
                        value={video.url}
                        onChange={(e) => {
                          const updatedVideos = [...values.videos];
                          updatedVideos[index] = { ...video, url: e.target.value };
                          setFieldValue('videos', updatedVideos);
                        }}
                        placeholder="Enter YouTube, Vimeo, or direct video URL"
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          const updatedVideos = values.videos.filter((_, i) => i !== index);
                          setFieldValue('videos', updatedVideos);
                        }}
                        className="mt-6"
                      >
                        <FaTrash className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  
                  {values.videos.length < 3 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        const newVideo = { url: "", title_en: "", title_ar: "" };
                        setFieldValue('videos', [...values.videos, newVideo]);
                      }}
                      className="flex items-center gap-2"
                    >
                      <FaPlus className="w-4 h-4" />
                      Add Video URL
                    </Button>
                  )}
                </div>
              </div>

              {/* Validation Errors */}
              {validationErrors[3] && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{validationErrors[3]}</p>
                </div>
              )}

              {/* Auto-save indicator */}
              <div className="text-center">
                <p className="text-sm text-gray-500">
                  Changes are automatically saved as you type
                </p>
              </div>
            </Form>
          )}
        </Formik>

        {/* Image Edit Modal */}
        {editingImage && (
          <ImageEditModal
            image={editingImage}
            onSave={(updatedImage) => {
              // Handle save logic here
              setEditingImage(null);
            }}
            onClose={() => setEditingImage(null)}
          />
        )}

        {/* Image Preview Modal */}
        {selectedImage && (
          <Modal isOpen={true} onClose={() => setSelectedImage(null)} size="lg">
            <div className="p-6">
              <img
                src={selectedImage.url}
                alt={selectedImage.title_en}
                className="w-full h-auto max-h-96 object-contain mx-auto"
              />
              <div className="mt-4 text-center">
                <h4 className="font-medium">{selectedImage.title_en}</h4>
                {selectedImage.title_ar && (
                  <p className="text-gray-600" dir="rtl">{selectedImage.title_ar}</p>
                )}
              </div>
            </div>
          </Modal>
        )}
      </Card>
    </motion.div>
  );
};

export default Step3MediaManagement;
