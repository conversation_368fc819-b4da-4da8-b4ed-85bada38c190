import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, useSearchParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/product/card";
import { Button } from "@/components/ui/product/button";

import {
  ChevronLeft,
  ChevronRight,
  Save,
  Check,
  HelpCircle,
  Package,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

// Import your existing step components
import StepProductDetails from "@/components/ProductWizard/steps/StepProductDetails";
import StepDetailsDescription from "@/components/ProductWizard/steps/StepDetailsDescription";
import StepProductMedia from "@/components/ProductWizard/steps/StepProductMedia";
import StepPricingInventory from "@/components/ProductWizard/steps/StepPricingInventory";
import StepComplianceFulfillment from "@/components/ProductWizard/steps/StepComplianceFulfillment";
import StepSEOFAQs from "@/components/ProductWizard/steps/StepSEOFAQs";
import StepReview from "@/components/ProductWizard/steps/StepReview";

// Define the product data interface
const STEPS = [
  { id: 1, title: "Classification", component: StepProductDetails },
  { id: 2, title: "Product Details", component: StepDetailsDescription },
  { id: 3, title: "Product Media", component: StepProductMedia },
  { id: 4, title: "Pricing & Inventory", component: StepPricingInventory },
  {
    id: 5,
    title: "Compliance & Fulfillment",
    component: StepComplianceFulfillment,
  },
  { id: 6, title: "SEO & FAQs", component: StepSEOFAQs },
  { id: 7, title: "Review & Submit", component: StepReview },
];

const ProductWizardPage = () => {
  const { id } = useParams(); // For /edit/{id} routes
  const [searchParams] = useSearchParams(); // For /add?id={id} routes
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Determine the product ID and mode
  const productId = id || searchParams.get('id');
  // Only consider it edit mode if we're on the /edit/{id} route, not /add?id={id}
  const isEditMode = Boolean(id) && location.pathname.includes('/edit/');

  const { toast } = useToast();
  const { fetchData, postMutation, putMutation } = useApi();

  const [currentStep, setCurrentStep] = useState(1);
  const [layoutMode, setLayoutMode] = useState("horizontal");
  const [loading, setLoading] = useState(false);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [validationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [createdProductId, setCreatedProductId] = useState(null);

  // Step descriptions for tooltips
  const stepDescriptions = {
    1: "Classify your product by category, brand, and define unique identifiers like SKU and barcode",
    2: "Set product titles, descriptions, and key product information",
    3: "Upload up to 6 product images. Drag to reorder, set one as primary, and add alt text for accessibility",
    4: "Configure pricing, inventory, and product variants",
    5: "Set compliance information, dietary certifications, and fulfillment logistics",
    6: "Configure SEO metadata and frequently asked questions for better discoverability",
    7: "Review all your product information before submitting for approval",
  };

  const [productData, setProductData] = useState({
    // Basic Product Information
    category_id: "",
    sub_category_id: "",
    class_id: "",
    sub_class_id: "",
    brand_id: "",
    vendor_sku: "",
    barcode: "",
    system_sku: "",
    model_number: "",

    // Product Details
    title_en: "",
    title_ar: "",
    short_name: "",
    short_description_en: "",
    short_description_ar: "",
    description_en: "",
    description_ar: "",

    // Product Specifications
    key_ingredients: "",
    usage_instructions: "",
    user_group: "",
    net_weight: "",
    net_weight_unit: "",
    formulation: "",
    servings: 0,
    flavour: "",

    // Media
    media: [],

    // Pricing & Inventory
    is_variant: false,
    regular_price: 0,
    offer_price: 0,
    discount_start_date: "",
    discount_end_date: "",
    vat_tax: "standard_5", // Changed to match API format
    approx_commission: 0,
    stock: 0,
    reserved: 0,
    threshold: 0,
    stock_status: "in_stock",
    warehouse_id: "",

    // Compliance & Certifications
    is_vegan: false,
    is_vegetarian: false,
    is_halal: false,
    allergen_info: "",
    storage_conditions: "",
    country_of_origin: "",
    bbe_date: "",
    regulatory_product_registration: "",
    vat_tax_url: "", // Fixed typo from vat_tax_utl

    // Package Dimensions
    package_length: 0,
    package_width: 0,
    package_height: 0,
    package_weight: 0,

    // Product Status
    status: "draft",
    is_active: 1,
    is_approved: 0,

    // Nested Objects for API
    product_seo: {
      meta_title_en: "",
      meta_title_ar: "",
      meta_description_en: "",
      meta_description_ar: "",
      keywords_en: "",
      keywords_ar: "",
    },

    product_faqs: [],

    product_fulfillment: {
      mode: "",
      is_returnable: false,
      collection_point: "",
      shipping_time: 0,
      shipping_fee: 0,
    },

    product_variants: [],

    // Legacy fields for backward compatibility
    attributes: [],
    variants: [],
    dietary_needs: "",
    slug: "",
  });

  // Fetch existing product data if editing OR if we have a product ID for persistence
  const shouldFetchProduct = isEditMode || (productId && searchParams.get('id'));
  const {
    data: existingProductData,
    isLoading: productLoading,
    isError: productError,
  } = shouldFetchProduct
    ? fetchData(`admin/products/${productId}`)
    : { data: null, isLoading: false, isError: false };

  // Initialize form data when product loads
  useEffect(() => {
    if (existingProductData?.data) {
      const product = existingProductData.data;

      // Transform API data to match our state structure
      const transformedData = {
        ...product,
        // Ensure nested objects exist
        product_seo: product.product_seo || {
          meta_title_en: product.meta_title_en || "",
          meta_title_ar: product.meta_title_ar || "",
          meta_description_en: product.meta_description_en || "",
          meta_description_ar: product.meta_description_ar || "",
          keywords_en: product.keywords_en || "",
          keywords_ar: product.keywords_ar || "",
        },
        product_faqs: product.product_faqs || product.faqs || [],
        product_fulfillment: product.product_fulfillment || {
          mode: product.mode || "",
          is_returnable: product.is_returnable || false,
          collection_point: product.collection_point || "",
          shipping_time: product.shipping_time || 0,
          shipping_fee: product.shipping_fee || 0,
        },
        product_variants: product.product_variants || product.variants || [],
        // Ensure required fields have defaults
        vat_tax: product.vat_tax || "standard_5",
        status: product.status || "draft",
        is_active: product.is_active !== undefined ? product.is_active : 1,
        is_approved: product.is_approved !== undefined ? product.is_approved : 0,
      };

      setProductData(transformedData);

      // Set createdProductId if we're in add mode with ID (for URL persistence)
      if (searchParams.get('id') && !isEditMode) {
        setCreatedProductId(product.id);
      }

      // Mark steps as completed based on available data
      const completed = new Set();
      if (product.category_id && product.sub_category_id && product.class_id && product.brand_id && product.vendor_sku && product.barcode) completed.add(1);
      if (product.title_en) completed.add(2);
      if (product.description_en) completed.add(3);
      if (product.media && product.media.length > 0) completed.add(4);
      if (product.regular_price) completed.add(5);
      if (product.product_fulfillment?.mode || product.mode) completed.add(6);
      if (product.product_seo?.meta_title_en || product.meta_title_en || product.product_faqs?.length > 0 || product.faqs?.length > 0) completed.add(7);
      setCompletedSteps(completed);
    }
  }, [existingProductData, isEditMode, searchParams]);

  // Remove auto-generation of system SKU - it should only come from the API after product creation

  // Auto-generate vendor SKU from barcode if empty
  useEffect(() => {
    if (
      !productData.vendor_sku &&
      productData.barcode &&
      productData.barcode.length >= 6
    ) {
      const generatedSku = productData.barcode.slice(-6);
      setProductData((prev) => ({
        ...prev,
        vendor_sku: `SKU-${generatedSku}`,
      }));
    }
  }, [productData.barcode, productData.vendor_sku]);



  // Hide validation when user fills required fields
  useEffect(() => {
    if (showValidation && validateCurrentStep(currentStep)) {
      setShowValidation(false);
    }
  }, [productData, currentStep, showValidation]);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showTooltip && !event.target.closest(".tooltip-container")) {
        setShowTooltip(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showTooltip]);

  // Transform form data to API format
  const transformDataForAPI = (data) => {
    const apiData = {
      // Basic fields
      barcode: data.barcode,
      model_number: data.model_number,
      brand_id: Number(data.brand_id),

      // Product details
      title_en: data.title_en,
      title_ar: data.title_ar,
      short_name: data.short_name,
      short_description_en: data.short_description_en,
      short_description_ar: data.short_description_ar,
      description_en: data.description_en,
      description_ar: data.description_ar,

      // Specifications
      key_ingredients: data.key_ingredients,
      usage_instructions: data.usage_instructions,
      user_group: data.user_group,
      net_weight: data.net_weight,
      net_weight_unit: data.net_weight_unit,
      formulation: data.formulation,
      servings: Number(data.servings) || 0,
      flavour: data.flavour,
      is_variant: Boolean(data.is_variant),

      // Pricing
      regular_price: Number(data.regular_price) || 0,
      offer_price: Number(data.offer_price) || 0,
      vat_tax: data.vat_tax || "standard_5",
      discount_start_date: data.discount_start_date,
      discount_end_date: data.discount_end_date,
      approx_commission: Number(data.approx_commission) || 0,

      // Compliance
      country_of_origin: data.country_of_origin,
      bbe_date: data.bbe_date,
      regulatory_product_registration: data.regulatory_product_registration,
      vat_tax_url: data.vat_tax_url,
      is_vegan: Boolean(data.is_vegan),
      is_vegetarian: Boolean(data.is_vegetarian),
      is_halal: Boolean(data.is_halal),
      allergen_info: data.allergen_info,
      storage_conditions: data.storage_conditions,

      // Package dimensions
      package_length: Number(data.package_length) || 0,
      package_width: Number(data.package_width) || 0,
      package_height: Number(data.package_height) || 0,
      package_weight: Number(data.package_weight) || 0,

      // Status
      status: data.status || "draft",
      is_active: Number(data.is_active) || 1,
      is_approved: Number(data.is_approved) || 0,

      // Nested objects
      product_seo: data.product_seo && Object.keys(data.product_seo).length > 0 ? data.product_seo : undefined,
      product_faqs: data.product_faqs && data.product_faqs.length > 0 ? data.product_faqs : undefined,
      product_fulfillment: data.product_fulfillment && Object.keys(data.product_fulfillment).length > 0 ? data.product_fulfillment : undefined,
      product_variants: data.product_variants && data.product_variants.length > 0 ? data.product_variants : undefined,
    };

    // Remove empty or undefined values
    Object.keys(apiData).forEach(key => {
      if (apiData[key] === "" || apiData[key] === undefined || apiData[key] === null) {
        delete apiData[key];
      }
    });

    return apiData;
  };

  const calculateProgress = () => {
    const totalFields = Object.keys(productData).length - 3;
    const filledFields = Object.entries(productData).filter(([key, value]) => {
      if (["media", "attributes", "variants", "product_faqs", "product_variants"].includes(key))
        return false;
      if (key === "system_sku") return false;
      if (typeof value === "object" && value !== null) {
        return Object.values(value).some(v => v !== "" && v !== 0 && v !== false);
      }
      return value !== "" && value !== 0 && value !== false;
    }).length;

    return Math.round((filledFields / totalFields) * 100);
  };

  // Validate complete product data for submission
  const validateCompleteProduct = () => {
    const errors = [];

    // Required basic fields
    if (!productData.category_id) errors.push("Product Category");
    if (!productData.sub_category_id) errors.push("Sub Category");
    if (!productData.class_id) errors.push("Product Class");
    if (!productData.brand_id) errors.push("Brand");
    if (!productData.vendor_sku) errors.push("Vendor SKU");
    if (!productData.barcode) errors.push("Barcode");
    if (!productData.model_number) errors.push("Model Number");

    // Required product details
    if (!productData.title_en) errors.push("Product Title (English)");
    if (!productData.description_en) errors.push("Product Description (English)");
    if (!productData.usage_instructions) errors.push("Usage Instructions");
    if (!productData.user_group) errors.push("User Group");
    if (!productData.net_weight) errors.push("Net Weight");
    if (!productData.net_weight_unit) errors.push("Net Weight Unit");

    // Required pricing
    if (!productData.regular_price || productData.regular_price <= 0) errors.push("Regular Price");
    if (!productData.vat_tax) errors.push("VAT Tax");

    // Required compliance
    if (productData.is_vegan === undefined || productData.is_vegan === null) errors.push("Vegan Status");
    if (productData.is_vegetarian === undefined || productData.is_vegetarian === null) errors.push("Vegetarian Status");
    if (productData.is_halal === undefined || productData.is_halal === null) errors.push("Halal Status");
    if (!productData.allergen_info) errors.push("Allergen Information");

    // Required fulfillment
    if (!productData.product_fulfillment?.mode) errors.push("Fulfillment Mode");
    if (!productData.product_fulfillment?.collection_point) errors.push("Collection Point");
    if (productData.product_fulfillment?.is_returnable === undefined || productData.product_fulfillment?.is_returnable === null) errors.push("Returnable Status");

    return errors;
  };

  const validateCurrentStep = (stepId) => {
    switch (stepId) {
      case 1: // Classification & Identity - MANDATORY: Product Category, Sub Category, Product Class, Brand Name, Product Code (SKU), Barcode
        return !!(
          productData.category_id &&
          productData.sub_category_id &&
          productData.class_id &&
          productData.brand_id &&
          productData.vendor_sku &&
          productData.barcode &&
          productData.model_number
        );
      case 2: // Details & Description - MANDATORY: Product Name/Title[en], Full Product Description[en], Usage Instructions, User Group, Net Weight, Net Weight Unit
        return !!(
          productData.title_en &&
          productData.description_en &&
          productData.usage_instructions &&
          productData.user_group &&
          productData.net_weight &&
          productData.net_weight_unit
        );
      case 3: // Product Media - Optional
        return true;
      case 4: // Pricing & Inventory - MANDATORY: Regular Price, VAT Tax
        return !!(
          productData.regular_price > 0 &&
          productData.vat_tax
        );
      case 5: // Compliance & Fulfillment - MANDATORY: Vegan, Vegetarian, Halal, Allergen Information, Fulfillment Mode, Collection Point, Returnable
        return !!(
          productData.is_vegan !== undefined &&
          productData.is_vegan !== null &&
          productData.is_vegetarian !== undefined &&
          productData.is_vegetarian !== null &&
          productData.is_halal !== undefined &&
          productData.is_halal !== null &&
          productData.allergen_info &&
          productData.product_fulfillment?.mode &&
          productData.product_fulfillment?.collection_point &&
          productData.product_fulfillment?.is_returnable !== undefined &&
          productData.product_fulfillment?.is_returnable !== null
        );
      case 6: // SEO & FAQs - Optional
        return true;
      case 7: // Review & Submit - Check overall completion
        return calculateProgress() >= 80;
      default:
        return true;
    }
  };

  // Function to create product after step 1 completion
  const createProduct = async () => {
    if (createdProductId || isEditMode) return; // Already created or in edit mode

    const requiredData = {
      category_id: Number(productData.category_id),
      sub_category_id: Number(productData.sub_category_id),
      class_id: Number(productData.class_id),
      sub_class_id: productData.sub_class_id ? Number(productData.sub_class_id) : null,
      vendor_sku: productData.vendor_sku,
      model_number: productData.model_number,
      brand_id: Number(productData.brand_id),
      barcode: productData.barcode,
    };

    try {
      const response = await postMutation.mutateAsync({
        endpoint: "admin/products",
        data: requiredData,
      });

      if (response?.data?.id) {
        setCreatedProductId(response.data.id);
        // Update productData with the system-generated SKU and ID
        setProductData(prev => ({
          ...prev,
          id: response.data.id,
          system_sku: response.data.system_sku,
        }));

        // Update URL to include the created product ID for data persistence
        const currentPath = location.pathname;
        if (currentPath === '/products/add') {
          navigate(`/products/add?id=${response.data.id}`, { replace: true });
        }

        toast({
          title: "Product Created",
          description: "Product has been created successfully. Continue with the next steps.",
        });
      }
    } catch (error) {
      console.error("Product creation failed:", error);
      toast({
        title: "Error",
        description: "Failed to create product: " + (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
      throw error; // Re-throw to prevent navigation
    }
  };

  const handleNext = async () => {
    // Show validation for the current step
    setShowValidation(true);
    setShowTooltip(false); // Hide tooltip when navigating

    if (validateCurrentStep(currentStep)) {
      setLoading(true);

      try {
        // Create product after completing step 1 (Classification)
        if (currentStep === 1 && !isEditMode && !createdProductId) {
          await createProduct();
        }

        setCompletedSteps((prev) => new Set([...prev, currentStep]));
        setShowValidation(false); // Hide validation when moving to next step
        const nextStepIndex = STEPS.findIndex((s) => s.id === currentStep) + 1;
        if (nextStepIndex < STEPS.length) {
          setCurrentStep(STEPS[nextStepIndex].id);
        }
      } catch (error) {
        // Error already handled in createProduct, just prevent navigation
        return;
      } finally {
        setLoading(false);
      }
    } else {
      toast({
        title: "Incomplete Step",
        description: "Please fill in all required fields before proceeding.",
        variant: "destructive",
      });
    }
  };

  const handlePrevious = () => {
    setShowTooltip(false); // Hide tooltip when navigating
    const currentIndex = STEPS.findIndex((s) => s.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1].id);
    }
  };

  const handleStepClick = (stepId) => {
    setCurrentStep(stepId);
    setShowValidation(false); // Hide validation when changing steps
    setShowTooltip(false); // Hide tooltip when changing steps
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      const apiData = transformDataForAPI({ ...productData, status: "draft" });

      if (isEditMode) {
        // Use productId which works for both /edit/{id} and /add?id={id}
        await putMutation.mutateAsync({
          endpoint: `admin/products/${productId}`,
          data: apiData,
        });
      } else if (createdProductId) {
        // Update the created product as draft
        await putMutation.mutateAsync({
          endpoint: `admin/products/${createdProductId}`,
          data: apiData,
        });
      } else {
        // Create product as draft if not created yet
        const response = await postMutation.mutateAsync({
          endpoint: "admin/products",
          data: apiData,
        });
        if (response?.data?.id) {
          setCreatedProductId(response.data.id);
        }
      }
      toast({
        title: "Draft Saved",
        description: "Your product has been saved as a draft.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Save draft failed:", error);
      toast({
        title: "Error",
        description:
          "Failed to save draft: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitForApproval = async () => {
    // Validate required fields
    const validationErrors = validateCompleteProduct();
    if (validationErrors.length > 0) {
      toast({
        title: "Missing Required Fields",
        description: `Please complete the following fields: ${validationErrors.slice(0, 3).join(', ')}${validationErrors.length > 3 ? ` and ${validationErrors.length - 3} more` : ''}`,
        variant: "destructive",
      });
      return;
    }

    if (calculateProgress() < 80) {
      toast({
        title: "Incomplete Product",
        description:
          "Please complete at least 80% of the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const apiData = transformDataForAPI({ ...productData, status: "submitted" });

      if (isEditMode) {
        // Use productId which works for both /edit/{id} and /add?id={id}
        await putMutation.mutateAsync({
          endpoint: `admin/products/${productId}`,
          data: apiData,
        });
      } else if (createdProductId) {
        // Update the created product with all the additional data
        await putMutation.mutateAsync({
          endpoint: `admin/products/${createdProductId}`,
          data: apiData,
        });
      } else {
        // Fallback: create product if somehow not created yet
        const response = await postMutation.mutateAsync({
          endpoint: "admin/products",
          data: apiData,
        });
        if (response?.data?.id) {
          setCreatedProductId(response.data.id);
        }
      }
      toast({
        title: "Submitted for Approval",
        description: "Your product has been submitted for review.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Submit failed:", error);
      toast({
        title: "Error",
        description:
          "Failed to submit product: " +
          (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStepComponent = () => {
    const currentStepData = STEPS.find((s) => s.id === currentStep);
    return currentStepData?.component || STEPS[0].component;
  };

  const CurrentStepComponent = getCurrentStepComponent();

  if (productLoading) {
    return <LoadingSpinner size={64} overlay />;
  }

  if (productError) {
    return (
      <div className="max-w-7xl mx-auto relative pt-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-red-800">
            Error Loading Product
          </h3>
          <p className="text-red-600 mt-2">
            Failed to load product data. Please try again or contact support.
          </p>
          <Button
            variant="outline"
            onClick={() => navigate("/products/all")}
            className="mt-4"
          >
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {loading && <LoadingSpinner size={64} overlay />}
      {/* Compact Header */}
      <div className="mb-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex flex-col">
                <h1 className="text-xl font-semibold text-gray-900">
                  {isEditMode
                    ? t("products.editProduct")
                    : t("products.createProduct")}
                </h1>
                {/* System SKU Display - Shows after product creation or during editing */}
                {productData.system_sku && (createdProductId || isEditMode) && (
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-green-50 border border-green-200 rounded-md">
                      <Package className="w-3.5 h-3.5 text-green-600" />
                      <span className="text-xs font-medium text-green-700">
                        System SKU:
                      </span>
                      <span className="text-xs font-mono font-semibold text-green-800">
                        {productData.system_sku}
                      </span>
                    </div>
                    {!isEditMode && createdProductId && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md">
                        <Check className="w-3.5 h-3.5 text-blue-600" />
                        <span className="text-xs font-medium text-blue-700">
                          Product Created
                        </span>
                      </div>
                    )}
                    {isEditMode && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-orange-50 border border-orange-200 rounded-md">
                        <Package className="w-3.5 h-3.5 text-orange-600" />
                        <span className="text-xs font-medium text-orange-700">
                          Edit Mode
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
                <span>
                  {t("products.step")} {currentStep}
                </span>
                <span>/</span>
                <span>{STEPS.length}</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="text-right">
                  <div className="text-lg font-bold text-blue-600">
                    {calculateProgress()}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {completedSteps.size}/{STEPS.length}
                  </div>
                </div>
                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-300"
                    style={{ width: `${calculateProgress()}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Step Indicator */}
      <div className="mb-4">
        <div className="bg-white rounded-lg border border-gray-200 p-3">
          <div className="hidden sm:block">
            <div className="flex items-center justify-between">
              {STEPS.map((step, index) => (
                <div key={step.id} className="flex items-center flex-1">
                  <div className="flex items-center">
                    <div
                      onClick={() => handleStepClick(step.id)}
                      className={`flex items-center justify-center w-8 h-8 rounded-full cursor-pointer transition-all duration-200 ${
                        currentStep === step.id
                          ? "bg-blue-600 text-white"
                          : completedSteps.has(step.id)
                          ? "bg-green-600 text-white"
                          : "bg-gray-200 text-gray-500 hover:bg-gray-300"
                      }`}
                    >
                      {completedSteps.has(step.id) ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <span className="text-xs font-semibold">
                          {index + 1}
                        </span>
                      )}
                    </div>
                    <div className="ml-2 hidden lg:block">
                      <p
                        className={`text-xs font-medium ${
                          currentStep === step.id
                            ? "text-blue-600"
                            : "text-gray-600"
                        }`}
                      >
                        {step.title}
                      </p>
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <div className="flex-1 mx-3">
                      <div
                        className={`h-0.5 rounded-full transition-all duration-300 ${
                          completedSteps.has(step.id)
                            ? "bg-green-400"
                            : "bg-gray-200"
                        }`}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Mobile View */}
          <div className="sm:hidden">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    currentStep === STEPS.find((s) => s.id === currentStep)?.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  <span className="text-xs font-semibold">{currentStep}</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {STEPS.find((s) => s.id === currentStep)?.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {t("products.step")} {currentStep} {t("products.of")}{" "}
                    {STEPS.length}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {STEPS.map((step) => (
                  <div
                    key={step.id}
                    onClick={() => handleStepClick(step.id)}
                    className={`w-2 h-2 rounded-full cursor-pointer transition-all duration-200 ${
                      currentStep === step.id
                        ? "bg-blue-600"
                        : completedSteps.has(step.id)
                        ? "bg-green-600"
                        : "bg-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile System SKU Display - Shows after product creation or during editing */}
      {productData.system_sku && (createdProductId || isEditMode) && (
        <div className="mb-4 sm:hidden">
          <div className="bg-white rounded-lg border border-gray-200 p-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-700">
                  System SKU:
                </span>
                <span className="text-sm font-mono font-semibold text-green-800">
                  {productData.system_sku}
                </span>
              </div>
              {!isEditMode && createdProductId && (
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">
                    Product Created - Adding Details
                  </span>
                </div>
              )}
              {isEditMode && (
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-700">
                    Editing Product
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <Card className="min-h-[600px]">
        <CardHeader className="border-b border-gray-200 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg font-semibold">
                {STEPS.find((s) => s.id === currentStep)?.title}
              </CardTitle>
              <div className="relative tooltip-container">
                <button
                  onClick={() => setShowTooltip(!showTooltip)}
                  className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                  aria-label="Step information"
                  aria-expanded={showTooltip}
                  aria-describedby={showTooltip ? "step-tooltip" : undefined}
                >
                  <HelpCircle className="w-3 h-3 text-gray-500" />
                </button>
                {showTooltip && (
                  <div
                    id="step-tooltip"
                    className="absolute left-0 top-6 z-[9999] w-80 max-w-[calc(100vw-2rem)] p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg sm:left-0 sm:w-80"
                    role="tooltip"
                  >
                    <div className="relative">
                      {stepDescriptions[currentStep]}
                      <div className="absolute -top-1 left-2 w-2 h-2 bg-gray-900 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setLayoutMode(
                    layoutMode === "horizontal" ? "vertical" : "horizontal"
                  )
                }
                className="text-xs px-2 py-1"
              >
                {layoutMode === "horizontal"
                  ? t("products.verticalLayout")
                  : t("products.horizontalLayout")}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <CurrentStepComponent
            data={productData}
            onChange={setProductData}
            layoutMode={layoutMode}
            validationErrors={validationErrors}
            showValidation={showValidation}
            onStepClick={currentStep === 7 ? handleStepClick : undefined}
          />
        </CardContent>
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === STEPS[0].id}
              className="flex items-center gap-1 text-sm px-3 py-2"
            >
              <ChevronLeft className="w-4 h-4" />
              {t("products.previous")}
            </Button>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                className="flex items-center gap-1 text-sm px-3 py-2"
                disabled={loading}
              >
                <Save className="w-4 h-4" />
                <span className="hidden sm:inline">
                  {t("products.saveDraft")}
                </span>
              </Button>

              {currentStep === STEPS[STEPS.length - 1].id ? (
                <Button
                  onClick={handleSubmitForApproval}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-sm px-3 py-2"
                  disabled={loading}
                >
                  <Check className="w-4 h-4" />
                  {t("products.submitForApproval")}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  className="flex items-center gap-1 text-sm px-3 py-2"
                  disabled={loading}
                >
                  {t("products.next")}
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProductWizardPage;
