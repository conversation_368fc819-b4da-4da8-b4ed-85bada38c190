import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormRadioGroup } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useApi } from "@/hooks/useApi";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

const EOIApprovedForm = ({ onSubmit, eoiId, vendorData, onCancel }) => {
  const { t } = useTranslation();
  const { postMutation } = useApi();

  const initialValues = {
    id: vendorData?.id || eoiId || "",
    status: vendorData?.status || "inactive",
  };

  const validationSchema = Yup.object({
    status: Yup.string().required(t("commonValidation.status")),
  });

  const statusOptions = [
    { label: t("commonOptions.yesNo.Yes"), value: "active" },
    { label: t("commonOptions.yesNo.No"), value: "inactive" },
  ];

  const handleSubmit = async (values, { setSubmitting }) => {
    // check not approved
    if (values.status === "inactive") {
      setSubmitting(false);
      onCancel();
      return;
    }

    postMutation.mutate(
      {
        endpoint: "/general/vendor-eoi/approve",
        data: { id: values.id },
      },
      {
        onSuccess: (res) => {
          if (res?.status && res?.data) {
            toast.success(
              res?.message || "Vendor EOI has been Approved successfully!"
            );
            onSubmit(res);
            onCancel();
          } else {
            toast.error(res?.message || "Vendor EOI has been Approved.");
          }
        },

        onSettled: () => {
          setSubmitting(false);
        },
      }
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting }) => (
        <Form className="space-y-4">
          <FormRadioGroup
            name="status"
            label={t("commonField.approval")}
            options={statusOptions}
            required
          />
          <div className="flex justify-end gap-2 pt-4 border-t border-gray-200 mt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {t("commonButton.approval")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default EOIApprovedForm;
