// src/data/menuItems.js
import {
  FaHome, FaShoppingCart, FaBox, FaUsers, FaCogs, FaChartBar, FaClipboardList, FaTags, FaList, FaUserTie, FaBlog, FaBullhorn, FaHeadset, FaGlobe, FaUserShield, FaTruck, FaGift, FaWpforms, FaTable, FaPager, FaBorderStyle, FaUsersCog, FaPlus, FaCog
} from 'react-icons/fa';
import * as permissions from '../constants/permissions';

const menuItems = [
  {
    path: '/dashboard',
    icon: FaHome,
    label: 'Dashboard',
    permissions: [permissions.BROWSE_ORDERS, permissions.BROWSE_PRODUCTS, permissions.BROWSE_USERS]
  },
  {
    label: 'Products',
    icon: FaBox,
    permissions: [permissions.BROWSE_PRODUCTS],
    children: [
      {
        path: '/products/all',
        label: 'All Products',
        icon: FaBox,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      // product circle
      {
        path: '/products/list',
        label: 'Products List',
        icon: FaBox,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/products/all',
        label: 'All Products',
        icon: FaBox,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/products/add',
        label: 'Add Product',
        icon: FaPlus,
        permissions: [permissions.CREATE_PRODUCT]
      },
      {
        path: '/products/categories',
        label: 'Category',
        icon: FaList,
        permissions: [permissions.BROWSE_CATEGORIES]
      },
      {
        path: '/products/class',
        label: 'Class',
        icon: FaTags,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/products/attribute',
        label: 'attribute',
        icon: FaBorderStyle,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      {
        path: '/brandManagement/brand',
        label: 'Brand',
        icon: FaTags,
        permissions: [permissions.BROWSE_PRODUCTS]
      },
      // {
      //   path: '/products/reviews',
      //   label: 'Product Reviews',
      //   icon: FaClipboardList,
      //   permissions: [permissions.BROWSE_REVIEWS]
      // }
    ]
  },
  // {
  //   label: 'Sales and Order',
  //   icon: FaShoppingCart,
  //   permissions: [permissions.BROWSE_ORDERS],
  //   children: [
  //     {
  //       path: '/orders/pending',
  //       label: 'Pending Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/orders/completed',
  //       label: 'Completed Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/orders/cancelled',
  //       label: 'Cancelled Orders',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     }
  //   ]
  // },
  // {
  //   path: '/customers',
  //   icon: FaUsers,
  //   label: 'Customers',
  //   permissions: [permissions.BROWSE_USERS]
  // },

  // {
  //   label: 'Configurations',
  //   icon: FaCogs,
  //   permissions: [permissions.BROWSE_ORDERS],
  //   children: [
  //     {
  //       path: '/configuration/banners',
  //       label: 'Banners',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //     {
  //       path: '/configuration/banner/items',
  //       label: 'banneritem',
  //       permissions: [permissions.BROWSE_ORDERS]
  //     },
  //   ]
  // },
  // {
  //   label: 'Partner Vendors',
  //   icon: FaUserTie,
  //   path: '/vendors',
  //   permissions: [permissions.BROWSE_VENDORS]
  // },
  // {
  //   label: 'Reports',
  //   icon: FaChartBar,
  //   permissions: [permissions.VIEW_SALES_REPORTS],
  //   children: [
  //     {
  //       path: '/reports/sales',
  //       label: 'Sales Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     },
  //     {
  //       path: '/reports/inventory',
  //       label: 'Inventory Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     },
  //     {
  //       path: '/reports/customers',
  //       label: 'Customer Reports',
  //       permissions: [permissions.ANALYZE_BUYER_BEHAVIOR]
  //     },
  //     {
  //       path: '/reports/vendors',
  //       label: 'Vendor Reports',
  //       permissions: [permissions.VIEW_SALES_REPORTS]
  //     }
  //   ]
  // },
  {
    label: 'Blog Management',
    icon: FaBlog,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
    children: [
      {
        path: '/blog/posts',
        // label: 'All Posts',
        label: 'Blog',
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
      {
        path: '/blog/categories',
        label: 'Categories',
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
      // {
      //   path: '/blog/comments',
      //   label: 'Comments',
      //   permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      // }
    ]
  },
  {
    label: 'Vendor Management',
    icon: FaUsersCog,
    permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT],
    children: [
      {
        path: '/vendor/eoi',
        label: 'vendor',
        permissions: [permissions.MANAGE_BLOG_POSTS_AND_CONTENT]
      },
    ]
  },
  {
    path: '/pages',
    icon: FaPager,
    label: 'Static Pages',
    permissions: [permissions.PAGE_VIEW]
  },
  // {
  //   label: 'Marketing',
  //   icon: FaBullhorn,
  //   permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS],
  //   children: [
  //     {
  //       path: '/marketing/campaigns',
  //       label: 'Campaigns',
  //       permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS]
  //     },
  //     {
  //       path: '/marketing/promotions',
  //       label: 'Promotions',
  //       permissions: [permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS]
  //     },
  //     {
  //       path: '/marketing/coupons',
  //       label: 'Coupons',
  //       permissions: [permissions.BROWSE_COUPONS]
  //     }
  //   ]
  // },
  // {
  //   label: 'Support',
  //   icon: FaHeadset,
  //   path: '/support',
  //   permissions: [permissions.BROWSE_SUPPORT_TICKETS]
  // },
  // {
  //   label: 'Homepage Setup',
  //   icon: FaGlobe,
  //   path: '/homepage-setup',
  //   permissions: [permissions.HOMEPAGE_EDIT]
  // },
  // {
  //   label: 'Users',
  //   icon: FaUserShield,
  //   path: '/staff/users',
  //   permissions: [permissions.STAFF_VIEW]
  // },
  {
    label: 'User Management',
    icon: FaUserShield,
    permissions: [permissions.STAFF_VIEW],
    children: [
      {
        path: '/staff/roles',
        label: 'Roles',
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/staff/permissions',
        label: 'Permissions',
        permissions: [permissions.STAFF_VIEW]
      },
      {
        path: '/staff/users',
        label: 'Users',
        permissions: [permissions.STAFF_VIEW]
      }
    ]
  },
  // {
  //   label: 'Delivery',
  //   icon: FaTruck,
  //   path: '/delivery',
  //   permissions: [permissions.BROWSE_SHIPMENTS]
  // },
  // {
  //   label: 'Loyalty and Reward Points',
  //   icon: FaGift,
  //   path: '/loyalty',
  //   permissions: [] // No direct equivalent in permissions.js
  // },
  // {
  //   label: 'Demo Form',
  //   icon: FaWpforms,
  //   path: '/demo/form',
  //   permissions: [] // No direct equivalent in permissions.js
  // },
  // {
  //   label: 'Demo List',
  //   icon: FaTable,
  //   path: '/demo/list',
  //   permissions: [] // No direct equivalent in permissions.js
  // }

  {
    label: 'Setting',
    icon: FaCog,
    permissions: [permissions.STAFF_VIEW],
    children: [
      {
        path: '/setting/menu/dropdown',
        label: 'DropDown Menu',
        permissions: [permissions.STAFF_VIEW]
      },
    ]
  },
];

export default menuItems;